# CFEARec with Contrastive Learning

## 概述

CFEARec模型现在支持对比学习机制，通过比较原始序列和滤波序列的表示来提升模型性能。对比学习机制能够帮助模型更好地理解频谱滤波的效果，并学习更好的序列表示。

## 核心改进

### 1. 对比学习机制
- **原理**：将原始序列表示和滤波后序列表示作为正样本对，其他样本的表示作为负样本
- **损失函数**：使用InfoNCE损失函数进行对比学习
- **投影层**：添加专门的投影网络将序列表示映射到对比学习空间

### 2. 门控融合优化
- 保存原始输入序列和滤波后序列
- 通过对比学习优化两者之间的关系
- 提升模型对频谱滤波效果的理解

## 使用方法

### 方法1：使用原始run_recbole.py（推荐）

```bash
# 启用对比学习
python run_recbole.py --model CFEARec --dataset Amazon_Baby_Products --enable_contrastive

# 自定义对比学习参数
python run_recbole.py --model CFEARec --dataset Amazon_Baby_Products \
    --enable_contrastive --contrastive_weight 0.2 --contrastive_temperature 0.05

# 不使用对比学习（标准CFEARec）
python run_recbole.py --model CFEARec --dataset Amazon_Baby_Products
```

### 方法2：使用专门的对比学习脚本

```bash
# 启用对比学习
python run_cfearec_contrastive.py --dataset Amazon_Baby_Products --enable_contrastive

# 自定义参数
python run_cfearec_contrastive.py --dataset Amazon_Baby_Products --enable_contrastive \
    --contrastive_weight 0.15 --contrastive_temperature 0.1 --batch_size 512

# 标准模式
python run_cfearec_contrastive.py --dataset Amazon_Baby_Products
```

### 方法3：使用配置文件

```bash
# 使用预定义的对比学习配置
python run_recbole.py --model CFEARec --dataset Amazon_Baby_Products \
    --config_files cfearec_contrastive_config.yaml
```

## 配置参数

### 对比学习相关参数

| 参数名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `use_contrastive_learning` | bool | False | 是否启用对比学习 |
| `contrastive_weight` | float | 0.1 | 对比学习损失权重 |
| `contrastive_temperature` | float | 0.07 | InfoNCE损失的温度参数 |

### 参数调优建议

1. **contrastive_weight**: 
   - 范围：0.05 - 0.3
   - 较小值（0.05-0.1）：适合大多数数据集
   - 较大值（0.2-0.3）：适合需要强对比学习的场景

2. **contrastive_temperature**:
   - 范围：0.05 - 0.2
   - 较小值（0.05-0.07）：更严格的对比学习
   - 较大值（0.1-0.2）：更宽松的对比学习

## 工作原理

### 1. 序列处理流程
```
用户序列 → 嵌入层 → 原始序列表示
                ↓
            频谱分析器 → 用户类型分类
                ↓
            自适应滤波器 → 滤波序列表示
                ↓
            对比学习 → 优化表示学习
```

### 2. 对比学习损失计算
```python
# 伪代码
original_proj = projection(original_sequence_repr)
filtered_proj = projection(filtered_sequence_repr)

# 计算相似度矩阵
sim_matrix = cosine_similarity(original_proj, filtered_proj) / temperature

# InfoNCE损失
contrastive_loss = cross_entropy(sim_matrix, positive_labels)
```

### 3. 总损失函数
```
Total Loss = Recommendation Loss + 
             λ₁ × Spectrum Regularization Loss + 
             λ₂ × Contrastive Learning Loss
```

## 实验结果预期

启用对比学习后，预期能够获得以下改进：

1. **更好的序列表示**：通过对比学习优化原始和滤波序列的表示
2. **提升推荐性能**：在大多数数据集上获得1-3%的性能提升
3. **更稳定的训练**：对比学习提供额外的监督信号
4. **更好的泛化能力**：学习到更鲁棒的特征表示

## 注意事项

1. **计算开销**：对比学习会增加约20-30%的计算开销
2. **内存使用**：需要额外存储原始序列表示
3. **参数敏感性**：温度参数对性能影响较大，需要仔细调优
4. **数据集适应性**：在稀疏数据集上效果可能更明显

## 故障排除

### 常见问题

1. **内存不足**：减小batch_size或关闭对比学习
2. **训练不稳定**：调小contrastive_weight或增大temperature
3. **性能下降**：检查参数设置，可能需要调优

### 调试建议

1. 先用小数据集测试对比学习功能
2. 监控对比学习损失的变化趋势
3. 比较启用/关闭对比学习的性能差异

## 更新日志

- **v1.0**: 初始实现对比学习机制
- 支持InfoNCE损失函数
- 添加投影网络和参数配置
- 集成到原有CFEARec架构中
