{"data": [{"category": "General Recommendation", "cate_link": "/docs/user_guide/model_intro.html#general-recommendation", "year": "2020", "pub": "WSDM'20", "model": "ADMMSLIM", "model_link": "/docs/user_guide/model/general/admmslim.html", "paper": "ADMM SLIM: Sparse Recommendations for Many Users", "paper_link": "https://doi.org/10.1145/3336191.3371774", "authors": "<PERSON>, <PERSON>, <PERSON><PERSON><PERSON> and <PERSON>", "ref_code": "", "repository": "RecBole", "repo_link": "https://github.com/RUCAIBox/RecBole"}, {"category": "General Recommendation", "cate_link": "/docs/user_guide/model_intro.html#general-recommendation", "year": "2009", "pub": "UAI'09", "model": "BPR", "model_link": "/docs/user_guide/model/general/bpr.html", "paper": "BPR: Bayesian Personalized Ranking from Implicit Feedback", "paper_link": "https://dl.acm.org/doi/10.5555/1795114.1795167", "authors": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON> and <PERSON>", "ref_code": "", "repository": "RecBole", "repo_link": "https://github.com/RUCAIBox/RecBole"}, {"category": "General Recommendation", "cate_link": "/docs/user_guide/model_intro.html#general-recommendation", "year": "2016", "pub": "WSDM'16", "model": "CDAE", "model_link": "/docs/user_guide/model/general/cdae.html", "paper": "Collaborative Denoising Auto-Encoders for Top-N Recommender Systems", "paper_link": "https://doi.org/10.1145/2835776.2835837", "authors": "<PERSON>, <PERSON>, <PERSON> and <PERSON>", "ref_code": "https://github.com/jasonyaw/CDAE", "repository": "RecBole", "repo_link": "https://github.com/RUCAIBox/RecBole"}, {"category": "General Recommendation", "cate_link": "/docs/user_guide/model_intro.html#general-recommendation", "year": "2018", "pub": "IJCAI'18", "model": "ConvNCF", "model_link": "/docs/user_guide/model/general/convncf.html", "paper": "Outer product-based neural collaborative filtering", "paper_link": "https://arxiv.org/pdf/1808.03912", "authors": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON>", "ref_code": "https://github.com/duxy-me/ConvNCF", "repository": "RecBole", "repo_link": "https://github.com/RUCAIBox/RecBole"}, {"category": "General Recommendation", "cate_link": "/docs/user_guide/model_intro.html#general-recommendation", "year": "2020", "pub": "SIGIR'20", "model": "DGCF", "model_link": "/docs/user_guide/model/general/dgcf.html", "paper": "Disentangled Graph Collaborative Filtering", "paper_link": "https://doi.org/10.1145/3397271.3401137", "authors": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON>", "ref_code": "https://github.com/xiangwang1223/disentangled_graph_collaborative_filtering", "repository": "RecBole", "repo_link": "https://github.com/RUCAIBox/RecBole"}, {"category": "General Recommendation", "cate_link": "/docs/user_guide/model_intro.html#general-recommendation", "year": "2017", "pub": "IJCAI'17", "model": "DMF", "model_link": "/docs/user_guide/model/general/dmf.html", "paper": "Deep Matrix Factorization Models for Recommender Systems", "paper_link": "http://www.ijcai.org/proceedings/2017/0447.pdf", "authors": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>", "ref_code": "", "repository": "RecBole", "repo_link": "https://github.com/RUCAIBox/RecBole"}, {"category": "General Recommendation", "cate_link": "/docs/user_guide/model_intro.html#general-recommendation", "year": "2019", "pub": "WWW'19", "model": "EASE", "model_link": "/docs/user_guide/model/general/ease.html", "paper": "Embarrassingly Shallow Autoencoders for Sparse Data", "paper_link": "https://doi.org/10.1145/3308558.3313710", "authors": "<PERSON>", "ref_code": "", "repository": "RecBole", "repo_link": "https://github.com/RUCAIBox/RecBole"}, {"category": "General Recommendation", "cate_link": "/docs/user_guide/model_intro.html#general-recommendation", "year": "2020", "pub": "TOIS'20", "model": "ENMF", "model_link": "/docs/user_guide/model/general/enmf.html", "paper": "Efficient Neural Matrix Factorization without Sampling for Recommendation", "paper_link": "https://doi.org/10.1145/3373807", "authors": "<PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>", "ref_code": "https://github.com/chenchongthu/ENMF", "repository": "RecBole", "repo_link": "https://github.com/RUCAIBox/RecBole"}, {"category": "General Recommendation", "cate_link": "/docs/user_guide/model_intro.html#general-recommendation", "year": "2013", "pub": "KDD'13", "model": "FISM", "model_link": "/docs/user_guide/model/general/fism.html", "paper": "FISM: factored item similarity models for top-N recommender systems", "paper_link": "https://doi.org/10.1145/2487575.2487589", "authors": "<PERSON><PERSON>, <PERSON><PERSON> and <PERSON>", "ref_code": "https://github.com/AaronHeee/Neural-Attentive-Item-Similarity-Model", "repository": "RecBole", "repo_link": "https://github.com/RUCAIBox/RecBole"}, {"category": "General Recommendation", "cate_link": "/docs/user_guide/model_intro.html#general-recommendation", "year": "2018", "pub": "SIGKDD'18", "model": "GCMC", "model_link": "/docs/user_guide/model/general/gcmc.html", "paper": "Graph Convolutional Matrix Completion", "paper_link": "http://arxiv.org/abs/1706.02263", "authors": "<PERSON><PERSON><PERSON>, <PERSON> and <PERSON>", "ref_code": "https://github.com/riannevdberg/gc-mc", "repository": "RecBole", "repo_link": "https://github.com/RUCAIBox/RecBole"}, {"category": "General Recommendation", "cate_link": "/docs/user_guide/model_intro.html#general-recommendation", "year": "2013", "pub": "RecSys'13", "model": "ItemKNN", "model_link": "/docs/user_guide/model/general/itemknn.html", "paper": "Efficient Top-N Recommendation for Very Large Scale Binary Rated Datasets", "paper_link": "https://doi.org/10.1145/2507157.2507189", "authors": "<PERSON><PERSON><PERSON>", "ref_code": "", "repository": "RecBole", "repo_link": "https://github.com/RUCAIBox/RecBole"}, {"category": "General Recommendation", "cate_link": "/docs/user_guide/model_intro.html#general-recommendation", "year": "2013", "pub": "RecSys'13", "model": "AsymKNN", "model_link": "/docs/user_guide/model/general/asymknn.html", "paper": "Efficient Top-N Recommendation for Very Large Scale Binary Rated Datasets", "paper_link": "https://doi.org/10.1145/2507157.2507189", "authors": "<PERSON><PERSON><PERSON>", "ref_code": "", "repository": "RecBole", "repo_link": "https://github.com/RUCAIBox/RecBole"}, {"category": "General Recommendation", "cate_link": "/docs/user_guide/model_intro.html#general-recommendation", "year": "2020", "pub": "SIGIR'20", "model": "LightGCN", "model_link": "/docs/user_guide/model/general/lightgcn.html", "paper": "LightGCN: Simplifying and Powering Graph Convolution Network for Recommendation", "paper_link": "https://doi.org/10.1145/3397271.3401063", "authors": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON>", "ref_code": "https://github.com/kuandeng/LightGCN", "repository": "RecBole", "repo_link": "https://github.com/RUCAIBox/RecBole"}, {"category": "General Recommendation", "cate_link": "/docs/user_guide/model_intro.html#general-recommendation", "year": "2015", "pub": "WWW'15", "model": "LINE", "model_link": "/docs/user_guide/model/general/line.html", "paper": "LINE: Large-scale Information Network Embedding", "paper_link": "https://doi.org/10.1145/2736277.2741093", "authors": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> and <PERSON><PERSON><PERSON><PERSON>", "ref_code": "https://github.com/shenweichen/GraphEmbedding", "repository": "RecBole", "repo_link": "https://github.com/RUCAIBox/RecBole"}, {"category": "General Recommendation", "cate_link": "/docs/user_guide/model_intro.html#general-recommendation", "year": "2019", "pub": "NIPS'19", "model": "MacridVAE", "model_link": "/docs/user_guide/model/general/macridvae.html", "paper": "Learning disentangled representations for recommendation", "paper_link": "https://dl.acm.org/doi/10.5555/3454287.3454800", "authors": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON>", "ref_code": "https://jianxinma.github.io/disentangle-recsys.html", "repository": "RecBole", "repo_link": "https://github.com/RUCAIBox/RecBole"}, {"category": "General Recommendation", "cate_link": "/docs/user_guide/model_intro.html#general-recommendation", "year": "2018", "pub": "WWW'18", "model": "MultiDAE", "model_link": "/docs/user_guide/model/general/multidae.html", "paper": "Variational Autoencoders for Collaborative Filtering", "paper_link": "https://doi.org/10.1145/3178876.3186150", "authors": "<PERSON><PERSON>, <PERSON><PERSON> <PERSON>, <PERSON> and <PERSON>", "ref_code": "", "repository": "RecBole", "repo_link": "https://github.com/RUCAIBox/RecBole"}, {"category": "General Recommendation", "cate_link": "/docs/user_guide/model_intro.html#general-recommendation", "year": "2018", "pub": "WWW'18", "model": "MultiVAE", "model_link": "/docs/user_guide/model/general/multivae.html", "paper": "Variational Autoencoders for Collaborative Filtering", "paper_link": "https://doi.org/10.1145/3178876.3186150", "authors": "<PERSON><PERSON>, <PERSON><PERSON> <PERSON>, <PERSON> and <PERSON>", "ref_code": "", "repository": "RecBole", "repo_link": "https://github.com/RUCAIBox/RecBole"}, {"category": "General Recommendation", "cate_link": "/docs/user_guide/model_intro.html#general-recommendation", "year": "2018", "pub": "TKDE'18", "model": "NAIS", "model_link": "/docs/user_guide/model/general/nais.html", "paper": "NAIS: Neural Attentive Item Similarity Model for Recommendation", "paper_link": "http://arxiv.org/abs/1809.07053", "authors": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON>", "ref_code": "https://github.com/AaronHeee/Neural-Attentive-Item-Similarity-Model", "repository": "RecBole", "repo_link": "https://github.com/RUCAIBox/RecBole"}, {"category": "General Recommendation", "cate_link": "/docs/user_guide/model_intro.html#general-recommendation", "year": "2019", "pub": "SIGIR'19", "model": "NCEPLRec", "model_link": "/docs/user_guide/model/general/nceplrec.html", "paper": "Noise Contrastive Estimation for One-Class Collaborative Filtering", "paper_link": "https://dl.acm.org/doi/epdf/10.1145/3331184.3331201", "authors": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON> and <PERSON><PERSON><PERSON>", "ref_code": "https://github.com/wuga214/NCE_Projected_LRec", "repository": "RecBole", "repo_link": "https://github.com/RUCAIBox/RecBole"}, {"category": "General Recommendation", "cate_link": "/docs/user_guide/model_intro.html#general-recommendation", "year": "2022", "pub": "WWW'22", "model": "NCL", "model_link": "/docs/user_guide/model/general/ncl.html", "paper": "Improving Graph Collaborative Filtering with Neighborhood-enriched Contrastive Learning", "paper_link": "http://arxiv.org/abs/2202.06200v2", "authors": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON>", "ref_code": "", "repository": "RecBole", "repo_link": "https://github.com/RUCAIBox/RecBole"}, {"category": "General Recommendation", "cate_link": "/docs/user_guide/model_intro.html#general-recommendation", "year": "2017", "pub": "WWW'17", "model": "NeuMF", "model_link": "/docs/user_guide/model/general/neumf.html", "paper": "Neural Collaborative Filtering", "paper_link": "http://dl.acm.org/doi/pdf/10.1145/3038912.3052569", "authors": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON>", "ref_code": "", "repository": "RecBole", "repo_link": "https://github.com/RUCAIBox/RecBole"}, {"category": "General Recommendation", "cate_link": "/docs/user_guide/model_intro.html#general-recommendation", "year": "2019", "pub": "SIGIR'19", "model": "NGCF", "model_link": "/docs/user_guide/model/general/ngcf.html", "paper": "Neural Graph Collaborative Filtering", "paper_link": "http://dl.acm.org/doi/pdf/10.1145/3331184.3331267", "authors": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON>", "ref_code": "https://github.com/xiangwang1223/neural_graph_collaborative_filtering", "repository": "RecBole", "repo_link": "https://github.com/RUCAIBox/RecBole"}, {"category": "General Recommendation", "cate_link": "/docs/user_guide/model_intro.html#general-recommendation", "year": "2017", "pub": "CIKM'17", "model": "NNCF", "model_link": "/docs/user_guide/model/general/nncf.html", "paper": "A Neural Collaborative Filtering Model with Interaction-based Neighborhood", "paper_link": "http://dl.acm.org/doi/pdf/10.1145/3132847.3133083", "authors": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON> and <PERSON>", "ref_code": "https://github.com/Tbbaby/NNCF-Pytorch", "repository": "RecBole", "repo_link": "https://github.com/RUCAIBox/RecBole"}, {"category": "General Recommendation", "cate_link": "/docs/user_guide/model_intro.html#general-recommendation", "year": "", "pub": "", "model": "Pop", "model_link": "/docs/user_guide/model/general/pop.html", "paper": "", "paper_link": "", "authors": "", "ref_code": "", "repository": "RecBole", "repo_link": "https://github.com/RUCAIBox/RecBole"}, {"category": "General Recommendation", "cate_link": "/docs/user_guide/model_intro.html#general-recommendation", "year": "2020", "pub": "ICLR'20", "model": "RaCT", "model_link": "/docs/user_guide/model/general/ract.html", "paper": "Towards Amortized Ranking-Critical Training for Collaborative Filtering", "paper_link": "https://arxiv.org/abs/1906.04281", "authors": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON>", "ref_code": "", "repository": "RecBole", "repo_link": "https://github.com/RUCAIBox/RecBole"}, {"category": "General Recommendation", "cate_link": "/docs/user_guide/model_intro.html#general-recommendation", "year": "2020", "pub": "WSDM'20", "model": "RecVAE", "model_link": "/docs/user_guide/model/general/recvae.html", "paper": "RecVAE: A New Variational Autoencoder for Top-N Recommendations with Implicit Feedback", "paper_link": "http://dl.acm.org/doi/epdf/10.1145/3336191.3371831", "authors": "<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON> and <PERSON>", "ref_code": "https://github.com/ilya-shenbin/RecVAE", "repository": "RecBole", "repo_link": "https://github.com/RUCAIBox/RecBole"}, {"category": "General Recommendation", "cate_link": "/docs/user_guide/model_intro.html#general-recommendation", "year": "2021", "pub": "SIGIR'21", "model": "SGL", "model_link": "/docs/user_guide/model/general/sgl.html", "paper": "Self-supervised Graph Learning for Recommendation", "paper_link": "https://arxiv.org/abs/2010.10783v4", "authors": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON>", "ref_code": "https://github.com/wujcan/SGL", "repository": "RecBole", "repo_link": "https://github.com/RUCAIBox/RecBole"}, {"category": "General Recommendation", "cate_link": "/docs/user_guide/model_intro.html#general-recommendation", "year": "2021", "pub": "CIKM'21", "model": "SimpleX", "model_link": "/docs/user_guide/model/general/simplex.html", "paper": "SimpleX: A Simple and Strong Baseline for Collaborative Filtering", "paper_link": "https://doi.org/10.1145/3459637.3482297", "authors": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON> and <PERSON><PERSON><PERSON><PERSON> He", "ref_code": "https://github.com/xue-pai/TwoToweRS", "repository": "RecBole", "repo_link": "https://github.com/RUCAIBox/RecBole"}, {"category": "General Recommendation", "cate_link": "/docs/user_guide/model_intro.html#general-recommendation", "year": "2011", "pub": "ICDM'11", "model": "SLIMElastic", "model_link": "/docs/user_guide/model/general/slimelastic.html", "paper": "SLIM: Sparse Linear Methods for Top-N Recommender Systems", "paper_link": "https://doi.org/10.1109/ICDM.2011.134", "authors": "<PERSON><PERSON> and <PERSON>", "ref_code": "https://github.com/KarypisLab/SLIM, https://github.com/MaurizioFD/RecSys2019_DeepLearning_Evaluation/blob/master/SLIM_ElasticNet/SLIMElasticNetRecommender.py", "repository": "RecBole", "repo_link": "https://github.com/RUCAIBox/RecBole"}, {"category": "General Recommendation", "cate_link": "/docs/user_guide/model_intro.html#general-recommendation", "year": "2018", "pub": "RecSys'18", "model": "SpectralCF", "model_link": "/docs/user_guide/model/general/spectralcf.html", "paper": "Spectral Collaborative Filtering", "paper_link": "https://arxiv.org/pdf/1808.10523.pdf", "authors": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON>", "ref_code": "https://github.com/lzheng21/SpectralCF", "repository": "RecBole", "repo_link": "https://github.com/RUCAIBox/RecBole"}, {"category": "Sequential Recommendation", "cate_link": "/docs/user_guide/model_intro.html#sequential-recommendation", "year": "2019", "pub": "CIKM'19", "model": "BERT4Rec", "model_link": "/docs/user_guide/model/sequential/bert4rec.html", "paper": "BERT4Rec: Sequential Recommendation with Bidirectional Encoder Representations from Transformer", "paper_link": "https://doi.org/10.1145/3357384.3357895", "authors": "<PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON> and <PERSON><PERSON>", "ref_code": "https://github.com/FeiSun/BERT4Rec", "repository": "RecBole", "repo_link": "https://github.com/RUCAIBox/RecBole"}, {"category": "Sequential Recommendation", "cate_link": "/docs/user_guide/model_intro.html#sequential-recommendation", "year": "2018", "pub": "WSDM'18", "model": "<PERSON><PERSON>", "model_link": "/docs/user_guide/model/sequential/caser.html", "paper": "Personalized Top-N Sequential Recommendation via Convolutional Sequence Embedding", "paper_link": "https://doi.org/10.1145/3357384.3357895", "authors": "<PERSON><PERSON><PERSON> and <PERSON>", "ref_code": "https://github.com/graytowne/caser_pytorch", "repository": "RecBole", "repo_link": "https://github.com/RUCAIBox/RecBole"}, {"category": "Sequential Recommendation", "cate_link": "/docs/user_guide/model_intro.html#sequential-recommendation", "year": "2022", "pub": "SIGIR'22", "model": "CORE", "model_link": "/docs/user_guide/model/sequential/core.html", "paper": "CORE: Simple and Effective Session-based Recommendation within Consistent Representation Space", "paper_link": "https://arxiv.org/abs/2204.11067", "authors": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> and <PERSON>", "ref_code": "https://github.com/RUCAIBox/CORE", "repository": "RecBole", "repo_link": "https://github.com/RUCAIBox/RecBole"}, {"category": "Sequential Recommendation", "cate_link": "/docs/user_guide/model_intro.html#sequential-recommendation", "year": "2019", "pub": "AAAI'19", "model": "DIEN", "model_link": "/docs/user_guide/model/sequential/dien.html", "paper": "Deep Interest Evolution Network for Click-Through Rate Prediction", "paper_link": "https://ojs.aaai.org/index.php/AAAI/article/view/4545", "authors": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON>", "ref_code": "https://github.com/mouna99/dien", "repository": "RecBole", "repo_link": "https://github.com/RUCAIBox/RecBole"}, {"category": "Sequential Recommendation", "cate_link": "/docs/user_guide/model_intro.html#sequential-recommendation", "year": "2018", "pub": "SIGKDD'18", "model": "DIN", "model_link": "/docs/user_guide/model/sequential/din.html", "paper": "Deep Interest Network for Click-Through Rate Prediction", "paper_link": "https://doi.org/10.1145/3219819.3219823", "authors": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON> and <PERSON><PERSON>", "ref_code": "https://github.com/zhougr1993/DeepInterestNetwork/tree/master/din", "repository": "RecBole", "repo_link": "https://github.com/RUCAIBox/RecBole"}, {"category": "Sequential Recommendation", "cate_link": "/docs/user_guide/model_intro.html#sequential-recommendation", "year": "2019", "pub": "IJCAI'19", "model": "FDSA", "model_link": "/docs/user_guide/model/sequential/fdsa.html", "paper": "Feature-level Deeper Self-Attention Network for Sequential Recommendation", "paper_link": "https://doi.org/10.5555/3367471.3367642", "authors": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>", "ref_code": "", "repository": "RecBole", "repo_link": "https://github.com/RUCAIBox/RecBole"}, {"category": "Sequential Recommendation", "cate_link": "/docs/user_guide/model_intro.html#sequential-recommendation", "year": "2016", "pub": "ICDM'16", "model": "FOSSIL", "model_link": "/docs/user_guide/model/sequential/fossil.html", "paper": "Fusing Similarity Models with Markov Chains for Sparse Sequential Recommendation", "paper_link": "https://ieeexplore.ieee.org/abstract/document/7837843", "authors": "T<PERSON><PERSON><PERSON> and <PERSON>", "ref_code": "", "repository": "RecBole", "repo_link": "https://github.com/RUCAIBox/RecBole"}, {"category": "Sequential Recommendation", "cate_link": "/docs/user_guide/model_intro.html#sequential-recommendation", "year": "2010", "pub": "WWW'10", "model": "FPMC", "model_link": "/docs/user_guide/model/sequential/fpmc.html", "paper": "Factorizing Personalized Markov Chains for Next-Basket Recommendation", "paper_link": "https://doi.org/10.1145/1772690.1772773", "authors": "<PERSON><PERSON><PERSON>, <PERSON> and <PERSON>", "ref_code": "", "repository": "RecBole", "repo_link": "https://github.com/RUCAIBox/RecBole"}, {"category": "Sequential Recommendation", "cate_link": "/docs/user_guide/model_intro.html#sequential-recommendation", "year": "2019", "pub": "IJCAI'19", "model": "GCSAN", "model_link": "/docs/user_guide/model/sequential/gcsan.html", "paper": "Graph Contextualized Self-Attention Network for Session-based Recommendation", "paper_link": "https://www.ijcai.org/proceedings/2019/0547.pdf", "authors": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON>", "ref_code": "", "repository": "RecBole", "repo_link": "https://github.com/RUCAIBox/RecBole"}, {"category": "Sequential Recommendation", "cate_link": "/docs/user_guide/model_intro.html#sequential-recommendation", "year": "2016", "pub": "DLRS'16", "model": "GRU4Rec", "model_link": "/docs/user_guide/model/sequential/gru4rec.html", "paper": "Improved Recurrent Neural Networks for Session-based Recommendations", "paper_link": "https://doi.org/10.1145/2988450.2988452", "authors": "<PERSON>, <PERSON><PERSON><PERSON> and <PERSON>", "ref_code": "", "repository": "RecBole", "repo_link": "https://github.com/RUCAIBox/RecBole"}, {"category": "Sequential Recommendation", "cate_link": "/docs/user_guide/model_intro.html#sequential-recommendation", "year": "2016", "pub": "RecSys'16", "model": "GRU4RecF", "model_link": "/docs/user_guide/model/sequential/gru4recf.html", "paper": "Parallel Recurrent Neural Network Architectures for Feature-rich Session-based Recommendations", "paper_link": "https://doi.org/10.1145/2959100.2959167", "authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>", "ref_code": "", "repository": "RecBole", "repo_link": "https://github.com/RUCAIBox/RecBole"}, {"category": "Sequential Recommendation", "cate_link": "/docs/user_guide/model_intro.html#sequential-recommendation", "year": "-", "pub": "-", "model": "GRU4RecKG", "model_link": "/docs/user_guide/model/sequential/gru4reckg.html", "paper": "-", "paper_link": "", "authors": "", "ref_code": "", "repository": "RecBole", "repo_link": "https://github.com/RUCAIBox/RecBole"}, {"category": "Sequential Recommendation", "cate_link": "/docs/user_guide/model_intro.html#sequential-recommendation", "year": "2019", "pub": "SIGKDD'19", "model": "HGN", "model_link": "/docs/user_guide/model/sequential/hgn.html", "paper": "Hierarchical Gating Networks for Sequential Recommendation", "paper_link": "https://doi.org/10.1145/3292500.3330984", "authors": "<PERSON>, <PERSON><PERSON> and <PERSON><PERSON>", "ref_code": "", "repository": "RecBole", "repo_link": "https://github.com/RUCAIBox/RecBole"}, {"category": "Sequential Recommendation", "cate_link": "/docs/user_guide/model_intro.html#sequential-recommendation", "year": "2015", "pub": "SIGIR'15", "model": "HRM", "model_link": "/docs/user_guide/model/sequential/hrm.html", "paper": "Learning Hierarchical Representation Model for Next Basket Recommendation", "paper_link": "https://doi.org/10.1145/2766462.2767694", "authors": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>", "ref_code": "https://github.com/wubinzzu/NeuRec", "repository": "RecBole", "repo_link": "https://github.com/RUCAIBox/RecBole"}, {"category": "Sequential Recommendation", "cate_link": "/docs/user_guide/model_intro.html#sequential-recommendation", "year": "2018", "pub": "SIGIR'18", "model": "KSR", "model_link": "/docs/user_guide/model/sequential/ksr.html", "paper": "Improving Sequential Recommendation with Knowledge-Enhanced Memory Networks", "paper_link": "https://doi.org/10.1145/3209978.3210017", "authors": "<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> and <PERSON>", "ref_code": "https://github.com/RUCAIBox/RecBole", "repository": "RecBole", "repo_link": "https://github.com/RUCAIBox/RecBole"}, {"category": "Sequential Recommendation", "cate_link": "/docs/user_guide/model_intro.html#sequential-recommendation", "year": "2021", "pub": "SIGIR'21", "model": "LightSANs", "model_link": "/docs/user_guide/model/sequential/lightsans.html", "paper": "Lighter and Better: Low-Rank Decomposed Self-Attention Networks for Next-Item Recommendation", "paper_link": "https://doi.org/10.1145/3404835.3462978", "authors": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>", "ref_code": "https://github.com/BELIEVEfxy/LightSANs", "repository": "RecBole", "repo_link": "https://github.com/RUCAIBox/RecBole"}, {"category": "Sequential Recommendation", "cate_link": "/docs/user_guide/model_intro.html#sequential-recommendation", "year": "2017", "pub": "CIKM'17", "model": "NARM", "model_link": "/docs/user_guide/model/sequential/narm.html", "paper": "Neural Attentive Session-based Recommendation", "paper_link": "https://doi.org/10.1145/3132847.3132926", "authors": "<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON> and <PERSON>", "ref_code": "https://github.com/<PERSON>-<PERSON>/Neural-Attentive-Session-Based-Recommendation-PyTorch", "repository": "RecBole", "repo_link": "https://github.com/RUCAIBox/RecBole"}, {"category": "Sequential Recommendation", "cate_link": "/docs/user_guide/model_intro.html#sequential-recommendation", "year": "2019", "pub": "WSDM'19", "model": "NextItNet", "model_link": "/docs/user_guide/model/sequential/nextitnet.html", "paper": "A Simple Convolutional Generative Network for Next Item Recommendation", "paper_link": "https://doi.org/10.1145/3289600.3290975", "authors": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON>", "ref_code": "https://github.com/fajieyuan/nextitnet", "repository": "RecBole", "repo_link": "https://github.com/RUCAIBox/RecBole"}, {"category": "Sequential Recommendation", "cate_link": "/docs/user_guide/model_intro.html#sequential-recommendation", "year": "2018", "pub": "IJCAI'18", "model": "NPE", "model_link": "/docs/user_guide/model/sequential/npe.html", "paper": "NPE: Neural Personalized Embedding for Collaborative Filtering", "paper_link": "https://doi.org/10.5555/3304415.3304640", "authors": "<PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>", "ref_code": "https://github.com/wubinzzu/NeuRec", "repository": "RecBole", "repo_link": "https://github.com/RUCAIBox/RecBole"}, {"category": "Sequential Recommendation", "cate_link": "/docs/user_guide/model_intro.html#sequential-recommendation", "year": "2019", "pub": "AAAI'19", "model": "RepeatNet", "model_link": "/docs/user_guide/model/sequential/repeatnet.html", "paper": "RepeatNet: A Repeat Aware Neural Recommendation Machine for Session-based Recommendation", "paper_link": "https://ojs.aaai.org/index.php/AAAI/article/view/4408", "authors": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON> and <PERSON><PERSON><PERSON>", "ref_code": "https://github.com/PengjieRen/RepeatNet", "repository": "RecBole", "repo_link": "https://github.com/RUCAIBox/RecBole"}, {"category": "Sequential Recommendation", "cate_link": "/docs/user_guide/model_intro.html#sequential-recommendation", "year": "2020", "pub": "CIKM'20", "model": "S3Rec", "model_link": "/docs/user_guide/model/sequential/s3rec.html", "paper": "S^3-Rec: Self-Supervised Learning for Sequential Recommendation with Mutual Information Maximization", "paper_link": "https://doi.org/10.1145/3340531.3411954", "authors": "<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>", "ref_code": "https://github.com/RUCAIBox/CIKM2020-S3Rec", "repository": "RecBole", "repo_link": "https://github.com/RUCAIBox/RecBole"}, {"category": "Sequential Recommendation", "cate_link": "/docs/user_guide/model_intro.html#sequential-recommendation", "year": "2018", "pub": "ICDM'18", "model": "SASRec", "model_link": "/docs/user_guide/model/sequential/sasrec.html", "paper": "Self-Attentive Sequential Recommendation", "paper_link": "https://doi.org/10.1109/ICDM.2018.00035", "authors": "<PERSON><PERSON><PERSON> and <PERSON>", "ref_code": "https://github.com/kang205/SASRec", "repository": "RecBole", "repo_link": "https://github.com/RUCAIBox/RecBole"}, {"category": "Context-aware Recommendation", "cate_link": "/docs/user_guide/model_intro.html#context-aware-recommendation", "year": "2017", "pub": "IJCAI' 18", "model": "AFM", "model_link": "/docs/user_guide/model/context/afm.html", "paper": "Attentional Factorization Machines: Learning the Weight of Feature Interactions via Attention Networks", "paper_link": "https://dl.acm.org/doi/abs/10.5555/3172077.3172324", "authors": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>", "ref_code": "", "repository": "RecBole", "repo_link": "https://github.com/RUCAIBox/RecBole"}, {"category": "Sequential Recommendation", "cate_link": "/docs/user_guide/model_intro.html#sequential-recommendation", "year": "-", "pub": "-", "model": "SASRecF", "model_link": "/docs/user_guide/model/sequential/sasrecf.html", "paper": "-", "paper_link": "", "authors": "", "ref_code": "", "repository": "RecBole", "repo_link": "https://github.com/RUCAIBox/RecBole"}, {"category": "Context-aware Recommendation", "cate_link": "/docs/user_guide/model_intro.html#context-aware-recommendation", "year": "2018", "pub": "CIKM' 18", "model": "AutoInt", "model_link": "/docs/user_guide/model/context/autoint.html", "paper": "AutoInt: Automatic Feature Interaction Learning via Self-Attentive Neural Networks", "paper_link": "https://dl.acm.org/doi/10.1145/3357384.3357925", "authors": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>", "ref_code": "", "repository": "RecBole", "repo_link": "https://github.com/RUCAIBox/RecBole"}, {"category": "Sequential Recommendation", "cate_link": "/docs/user_guide/model_intro.html#sequential-recommendation", "year": "2018", "pub": "IJCAI'18", "model": "SHAN", "model_link": "/docs/user_guide/model/sequential/shan.html", "paper": "Sequential Recommender System based on Hierarchical Attention Network", "paper_link": "https://opus.lib.uts.edu.au/handle/10453/126040", "authors": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON> and <PERSON><PERSON>", "ref_code": "", "repository": "RecBole", "repo_link": "https://github.com/RUCAIBox/RecBole"}, {"category": "Context-aware Recommendation", "cate_link": "/docs/user_guide/model_intro.html#context-aware-recommendation", "year": "2017", "pub": "ADKDD' 17", "model": "DCN", "model_link": "/docs/user_guide/model/context/dcn.html", "paper": "Deep & Cross Network for Ad Click Predictions", "paper_link": "https://dl.acm.org/doi/10.1145/3124749.3124754", "authors": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>", "ref_code": "https://github.com/shenweichen/DeepCTR-Torch", "repository": "RecBole", "repo_link": "https://github.com/RUCAIBox/RecBole"}, {"category": "Context-aware Recommendation", "cate_link": "/docs/user_guide/model_intro.html#context-aware-recommendation", "year": "2021", "pub": "WWW' 21", "model": "DCNV2", "model_link": "/docs/user_guide/model/context/dcnv2.html", "paper": "DCN V2: Improved Deep & Cross Network and Practical Lessons\nforWeb-scale Learning to Rank Systems", "paper_link": "https://dl.acm.org/doi/10.1145/3442381.3450078", "authors": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>", "ref_code": "https://github.com/shenweichen/DeepCTR-Torch", "repository": "RecBole", "repo_link": "https://github.com/RUCAIBox/RecBole"}, {"category": "Context-aware Recommendation", "cate_link": "/docs/user_guide/model_intro.html#context-aware-recommendation", "year": "2017", "pub": "IJCAI' 17", "model": "DeepFM", "model_link": "/docs/user_guide/model/context/deepfm.html", "paper": "DeepFM: A Factorization-Machine based Neural Network for CTR Prediction", "paper_link": "https://dl.acm.org/doi/abs/10.5555/3172077.3172127", "authors": "<PERSON><PERSON> , <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>", "ref_code": "", "repository": "RecBole", "repo_link": "https://github.com/RUCAIBox/RecBole"}, {"category": "Sequential Recommendation", "cate_link": "/docs/user_guide/model_intro.html#sequential-recommendation", "year": "2021", "pub": "WSDM'21", "model": "SINE", "model_link": "/docs/user_guide/model/sequential/sine.html", "paper": "Sparse-Interest Network for Sequential Recommendation", "paper_link": "https://doi.org/10.1145/3437963.3441811", "authors": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON>", "ref_code": "", "repository": "RecBole", "repo_link": "https://github.com/RUCAIBox/RecBole"}, {"category": "Context-aware Recommendation", "cate_link": "/docs/user_guide/model_intro.html#context-aware-recommendation", "year": "2019", "pub": "AAAI' 19", "model": "DIEN", "model_link": "/docs/user_guide/model/context/dien.html", "paper": "Deep Interest Evolution Network for Click-Through Rate Prediction", "paper_link": "https://doi.org/10.1609/aaai.v33i01.33015941", "authors": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>", "ref_code": "https://github.com/shenweichen/DeepCTR-Torch/", "repository": "RecBole", "repo_link": "https://github.com/RUCAIBox/RecBole"}, {"category": "Sequential Recommendation", "cate_link": "/docs/user_guide/model_intro.html#sequential-recommendation", "year": "2019", "pub": "AAAI'19", "model": "SRGNN", "model_link": "/docs/user_guide/model/sequential/srgnn.html", "paper": "Session-based Recommendation with Graph Neural Networks", "paper_link": "https://ojs.aaai.org/index.php/AAAI/article/view/3804", "authors": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON>", "ref_code": "https://github.com/CRIPAC-DIG/SR-GNN", "repository": "RecBole", "repo_link": "https://github.com/RUCAIBox/RecBole"}, {"category": "Context-aware Recommendation", "cate_link": "/docs/user_guide/model_intro.html#context-aware-recommendation", "year": "2018", "pub": "SIGKDD' 18", "model": "DIN", "model_link": "/docs/user_guide/model/context/din.html", "paper": "Deep Interest Network for Click-Through Rate Prediction", "paper_link": "https://dl.acm.org/doi/10.1145/3219819.3219823", "authors": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>", "ref_code": "https://github.com/shenweichen/DeepCTR-Torch/tree/master/deepctr_torch/models", "repository": "RecBole", "repo_link": "https://github.com/RUCAIBox/RecBole"}, {"category": "Sequential Recommendation", "cate_link": "/docs/user_guide/model_intro.html#sequential-recommendation", "year": "2018", "pub": "SIGKDD'18", "model": "STAMP", "model_link": "/docs/user_guide/model/sequential/stamp.html", "paper": "STAMP: Short-Term Attention/Memory Priority Model for Session-based Recommendation", "paper_link": "https://doi.org/10.1145/3219819.3219950", "authors": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON>", "ref_code": "", "repository": "RecBole", "repo_link": "https://github.com/RUCAIBox/RecBole"}, {"category": "Context-aware Recommendation", "cate_link": "/docs/user_guide/model_intro.html#context-aware-recommendation", "year": "2013", "pub": "CIKM' 13", "model": "DSSM", "model_link": "/docs/user_guide/model/context/dssm.html", "paper": "Learning deep structured semantic models for web search using clickthrough data", "paper_link": "https://dl.acm.org/doi/10.1145/2505515.2505665", "authors": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>", "ref_code": "", "repository": "RecBole", "repo_link": "https://github.com/RUCAIBox/RecBole"}, {"category": "Sequential Recommendation", "cate_link": "/docs/user_guide/model_intro.html#sequential-recommendation", "year": "2017", "pub": "RecSys'17", "model": "TransRec", "model_link": "/docs/user_guide/model/sequential/transrec.html", "paper": "Translation-based Recommendation", "paper_link": "https://doi.org/10.1145/3109859.3109882", "authors": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON>", "ref_code": "", "repository": "RecBole", "repo_link": "https://github.com/RUCAIBox/RecBole"}, {"category": "Context-aware Recommendation", "cate_link": "/docs/user_guide/model_intro.html#context-aware-recommendation", "year": "2016", "pub": "RecSys' 18", "model": "FFM", "model_link": "/docs/user_guide/model/context/ffm.html", "paper": "Field-aware Factorization Machines for CTR Prediction", "paper_link": "https://dl.acm.org/doi/10.1145/2959100.2959134", "authors": "<PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>", "ref_code": "https://github.com/rixwew/pytorch-fm", "repository": "RecBole", "repo_link": "https://github.com/RUCAIBox/RecBole"}, {"category": "Context-aware Recommendation", "cate_link": "/docs/user_guide/model_intro.html#context-aware-recommendation", "year": "2010", "pub": "ICDM' 10", "model": "FM", "model_link": "/docs/user_guide/model/context/fm.html", "paper": "Factorization Machines", "paper_link": "https://ieeexplore.ieee.org/abstract/document/5694074/", "authors": "Steffen Rendle", "ref_code": "", "repository": "RecBole", "repo_link": "https://github.com/RUCAIBox/RecBole"}, {"category": "Context-aware Recommendation", "cate_link": "/docs/user_guide/model_intro.html#context-aware-recommendation", "year": "2016", "pub": "ECIR' 16", "model": "FNN", "model_link": "/docs/user_guide/model/context/fnn.html", "paper": "Deep Learning over Multi-field Categorical Data", "paper_link": "https://link.springer.com/chapter/10.1007/978-3-319-30671-1_4", "authors": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>", "ref_code": "", "repository": "RecBole", "repo_link": "https://github.com/RUCAIBox/RecBole"}, {"category": "Context-aware Recommendation", "cate_link": "/docs/user_guide/model_intro.html#context-aware-recommendation", "year": "2018", "pub": "WWW' 18", "model": "FwFM", "model_link": "/docs/user_guide/model/context/fwfm.html", "paper": "Field-weighted Factorization Machines for Click-Through Rate Prediction in Display Advertising", "paper_link": "https://dl.acm.org/doi/10.1145/3178876.3186040", "authors": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>", "ref_code": "", "repository": "RecBole", "repo_link": "https://github.com/RUCAIBox/RecBole"}, {"category": "Context-aware Recommendation", "cate_link": "/docs/user_guide/model_intro.html#context-aware-recommendation", "year": "2007", "pub": "WWW' 07", "model": "LR", "model_link": "/docs/user_guide/model/context/lr.html", "paper": "Predicting Clicks Estimating the Click-Through Rate for New Ads", "paper_link": "https://dl.acm.org/doi/10.1145/1242572.1242643", "authors": "<PERSON>, <PERSON><PERSON>, <PERSON>", "ref_code": "", "repository": "RecBole", "repo_link": "https://github.com/RUCAIBox/RecBole"}, {"category": "Context-aware Recommendation", "cate_link": "/docs/user_guide/model_intro.html#context-aware-recommendation", "year": "2017", "pub": "SIGIR' 17", "model": "NFM", "model_link": "/docs/user_guide/model/context/nfm.html", "paper": "Neural Factorization Machines for Sparse Predictive Analytics", "paper_link": "https://dl.acm.org/doi/abs/10.1145/3077136.3080777", "authors": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>", "ref_code": "", "repository": "RecBole", "repo_link": "https://github.com/RUCAIBox/RecBole"}, {"category": "Context-aware Recommendation", "cate_link": "/docs/user_guide/model_intro.html#context-aware-recommendation", "year": "2016", "pub": "ICDM' 16", "model": "PNN", "model_link": "/docs/user_guide/model/context/pnn.html", "paper": "Product-based neural networks for user response prediction", "paper_link": "https://ieeexplore.ieee.org/abstract/document/7837964/", "authors": "<PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>", "ref_code": "", "repository": "RecBole", "repo_link": "https://github.com/RUCAIBox/RecBole"}, {"category": "Context-aware Recommendation", "cate_link": "/docs/user_guide/model_intro.html#context-aware-recommendation", "year": "2016", "pub": "RecSys' 16", "model": "WideDeep", "model_link": "/docs/user_guide/model/context/widedeep.html", "paper": "Wide & Deep Learning for Recommender Systems", "paper_link": "https://dl.acm.org/doi/10.1145/2988450.2988454", "authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>", "ref_code": "", "repository": "RecBole", "repo_link": "https://github.com/RUCAIBox/RecBole"}, {"category": "Context-aware Recommendation", "cate_link": "/docs/user_guide/model_intro.html#context-aware-recommendation", "year": "2018", "pub": "SIGKDD' 18", "model": "xDeepFM", "model_link": "/docs/user_guide/model/context/xdeepfm.html", "paper": "xDeepFM: Combining Explicit and Implicit Feature Interactions for Recommender Systems", "paper_link": "https://dl.acm.org/doi/10.1145/3219819.3220023", "authors": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>", "ref_code": "", "repository": "RecBole", "repo_link": "https://github.com/RUCAIBox/RecBole"}, {"category": "Context-aware Recommendation", "cate_link": "/docs/user_guide/model_intro.html#context-aware-recommendation", "year": "2019", "pub": "CIKM' 19", "model": "FiGNN", "model_link": "/docs/user_guide/model/context/fignn.html", "paper": "Fi-GNN: Modeling Feature Interactions via Graph Neural Networks for CTR Prediction", "paper_link": "https://dl.acm.org/doi/10.1145/3357384.3357951", "authors": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>", "ref_code": "https://github.com/CRIPAC-DIG/GraphCTR, https://github.com/xue-pai/FuxiCTR", "repository": "RecBole", "repo_link": "https://github.com/RUCAIBox/RecBole"}, {"category": "Context-aware Recommendation", "cate_link": "/docs/user_guide/model_intro.html#context-aware-recommendation", "year": "2023", "pub": "WSDM' 23", "model": "KD_DAGFM", "model_link": "/docs/user_guide/model/context/kd_dagfm.html", "paper": "Directed Acyclic Graph Factorization Machines for CTR Prediction via Knowledge Distillation", "paper_link": "https://arxiv.org/abs/2211.11159", "authors": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>", "ref_code": "https://github.com/chenyuwuxin/DAGFM", "repository": "RecBole", "repo_link": "https://github.com/RUCAIBox/RecBole"}, {"category": "Data Augmentation", "cate_link": "https://github.com/RUCAIBox/RecBole2.0#recbole-da", "year": "2022", "pub": "ICDE'22", "model": "CL4SRec", "model_link": "https://github.com/RUCAIBox/RecBole-DA/blob/master/recbole/model/sequential_recommender/cl4srec.py", "paper": "Contrastive Learning for Sequential Recommendation", "paper_link": "https://ieeexplore.ieee.org/abstract/document/9835621", "authors": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON>", "ref_code": "", "repository": "RecBole-DA", "repo_link": "https://github.com/RUCAIBox/RecBole-DA"}, {"category": "Data Augmentation", "cate_link": "https://github.com/RUCAIBox/RecBole2.0#recbole-da", "year": "2022", "pub": "WSDM'22", "model": "DuoRec", "model_link": "https://github.com/RUCAIBox/RecBole-DA/blob/master/recbole/model/sequential_recommender/duorec.py", "paper": "Contrastive Learning for Representation Degeneration Problem in Sequential Recommendation", "paper_link": "https://doi.org/10.1145/3488560.3498433", "authors": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON>", "ref_code": "https://github.com/RuihongQiu/DuoRec", "repository": "RecBole-DA", "repo_link": "https://github.com/RUCAIBox/RecBole-DA"}, {"category": "Data Augmentation", "cate_link": "https://github.com/RUCAIBox/RecBole2.0#recbole-da", "year": "2021", "pub": "ICDM'21", "model": "MMInfoRec", "model_link": "https://github.com/RUCAIBox/RecBole-DA/tree/master/recbole/model/sequential_recommender/MMInfoRec", "paper": "Memory Augmented Multi-Instance Contrastive Predictive Coding for Sequential Recommendation", "paper_link": "https://ieeexplore.ieee.org/abstract/document/9678990", "authors": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON>", "ref_code": "https://github.com/RuihongQiu/MMInfoRec", "repository": "RecBole-DA", "repo_link": "https://github.com/RUCAIBox/RecBole-DA"}, {"category": "Data Augmentation", "cate_link": "https://github.com/RUCAIBox/RecBole2.0#recbole-da", "year": "2021", "pub": "CIKM'21", "model": "CCL", "model_link": "https://github.com/RUCAIBox/RecBole-DA/tree/master/recbole/model/sequential_recommender/ccl", "paper": "Contrastive Curriculum Learning for Sequential User Behavior Modeling via Data Augmentation", "paper_link": "https://doi.org/10.1145/3459637.3481905", "authors": "<PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>", "ref_code": "https://github.com/RUCAIBox/Contrastive-Curriculum-Learning", "repository": "RecBole-DA", "repo_link": "https://github.com/RUCAIBox/RecBole-DA"}, {"category": "Meta recommendation", "cate_link": "https://github.com/RUCAIBox/RecBole2.0#recbole-metarec", "year": "2019", "pub": "SIGKDD'19", "model": "MeLU", "model_link": "https://github.com/nuster1128/RecBole-MetaRec/blob/master/recbole_metarec/model/MeLU", "paper": "MeLU: Meta-Learned User Preference Estimator for Cold-Start Recommendation", "paper_link": "https://doi.org/10.1145/3292500.3330859", "authors": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON>", "ref_code": "https://github.com/hoyeoplee/MeLU", "repository": "RecBole-MetaRec", "repo_link": "https://github.com/nuster1128/RecBole-MetaRec"}, {"category": "Meta recommendation", "cate_link": "https://github.com/RUCAIBox/RecBole2.0#recbole-metarec", "year": "2020", "pub": "SIGKDD'20", "model": "MAMO", "model_link": "https://github.com/nuster1128/RecBole-MetaRec/blob/master/recbole_metarec/model/MAMO", "paper": "MAMO: Memory-Augmented Meta-Optimization for Cold-start Recommendation", "paper_link": "https://doi.org/10.1145/3394486.3403113", "authors": "Manqing Dong, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON> and Liming Zhu", "ref_code": "https://github.com/dongmanqing/Code-for-MAMO", "repository": "RecBole-MetaRec", "repo_link": "https://github.com/nuster1128/RecBole-MetaRec"}, {"category": "Meta recommendation", "cate_link": "https://github.com/RUCAIBox/RecBole2.0#recbole-metarec", "year": "2021", "pub": "WWW'21", "model": "TaNP", "model_link": "https://github.com/nuster1128/RecBole-MetaRec/blob/master/recbole_metarec/model/TaNP", "paper": "Task-adaptive Neural Process for User Cold-Start Recommendation", "paper_link": "https://doi.org/10.1145/3442381.3449908", "authors": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON>", "ref_code": "https://github.com/IIEdm/TaNP", "repository": "RecBole-MetaRec", "repo_link": "https://github.com/nuster1128/RecBole-MetaRec"}, {"category": "Meta recommendation", "cate_link": "https://github.com/RUCAIBox/RecBole2.0#recbole-metarec", "year": "2017", "pub": "NIPS'17", "model": "LWA", "model_link": "https://github.com/nuster1128/RecBole-MetaRec/blob/master/recbole_metarec/model/LWA", "paper": "A Meta-Learning Perspective on Cold-Start Recommendations for Items", "paper_link": "https://proceedings.neurips.cc/paper/2017/hash/51e6d6e679953c6311757004d8cbbba9-Abstract.html", "authors": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON>", "ref_code": "", "repository": "RecBole-MetaRec", "repo_link": "https://github.com/nuster1128/RecBole-MetaRec"}, {"category": "Meta recommendation", "cate_link": "https://github.com/RUCAIBox/RecBole2.0#recbole-metarec", "year": "2017", "pub": "NIPS'17", "model": "NLBA", "model_link": "https://github.com/nuster1128/RecBole-MetaRec/blob/master/recbole_metarec/model/NLBA", "paper": "A Meta-Learning Perspective on Cold-Start Recommendations for Items", "paper_link": "https://proceedings.neurips.cc/paper/2017/hash/51e6d6e679953c6311757004d8cbbba9-Abstract.html", "authors": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON>", "ref_code": "", "repository": "RecBole-MetaRec", "repo_link": "https://github.com/nuster1128/RecBole-MetaRec"}, {"category": "Meta recommendation", "cate_link": "https://github.com/RUCAIBox/RecBole2.0#recbole-metarec", "year": "2019", "pub": "SIGIR'19", "model": "MetaEmb", "model_link": "https://github.com/nuster1128/RecBole-MetaRec/blob/master/recbole_metarec/model/MetaEmb", "paper": "Warm Up Cold-start Advertisements: Improving CTR Predictions via Learning to Learn ID Embeddings", "paper_link": "https://doi.org/10.1145/3331184.3331268", "authors": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON>", "ref_code": "https://github.com/Feiyang/MetaEmbedding", "repository": "RecBole-MetaRec", "repo_link": "https://github.com/nuster1128/RecBole-MetaRec"}, {"category": "Meta recommendation", "cate_link": "https://github.com/RUCAIBox/RecBole2.0#recbole-metarec", "year": "2021", "pub": "SIGIR'21", "model": "MWUF", "model_link": "https://github.com/nuster1128/RecBole-MetaRec/blob/master/recbole_metarec/model/MWUF", "paper": "Learning to Warm Up Cold Item Embeddings for Cold-start Recommendation with Meta Scaling and Shifting Networks", "paper_link": "https://doi.org/10.1145/3404835.3462843", "authors": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON> and <PERSON>", "ref_code": "", "repository": "RecBole-MetaRec", "repo_link": "https://github.com/nuster1128/RecBole-MetaRec"}, {"category": "Debiased recommendation", "cate_link": "https://github.com/RUCAIBox/RecBole2.0#recbole-debias", "year": "2009", "pub": "Computer'09", "model": "MF", "model_link": "https://github.com/JingsenZhang/Recbole-Debias/blob/master/recbole_debias/model/debiased_recommender/mf.py", "paper": "Matrix Factorization Techniques for Recommender Systems", "paper_link": "https://ieeexplore.ieee.org/abstract/document/5197422", "authors": "<PERSON><PERSON><PERSON>, <PERSON> and <PERSON>", "ref_code": "", "repository": "RecBole-<PERSON><PERSON><PERSON>", "repo_link": "https://github.com/Jingsen<PERSON>hang/Recbole-Debias"}, {"category": "Debiased recommendation", "cate_link": "https://github.com/RUCAIBox/RecBole2.0#recbole-debias", "year": "2016", "pub": "ICML'16", "model": "MF-IPS", "model_link": "https://github.com/JingsenZhang/Recbole-Debias/blob/master/recbole_debias/model/debiased_recommender/mf_ips.py", "paper": "Recommendations as Treatments: Debiasing Learning and Evaluation", "paper_link": "http://proceedings.mlr.press/v48/schnabel16.html?ref=https://githubhelp.com", "authors": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON>", "ref_code": "", "repository": "RecBole-<PERSON><PERSON><PERSON>", "repo_link": "https://github.com/Jingsen<PERSON>hang/Recbole-Debias"}, {"category": "Debiased recommendation", "cate_link": "https://github.com/RUCAIBox/RecBole2.0#recbole-debias", "year": "2021", "pub": "SIGIR'21", "model": "PDA", "model_link": "https://github.com/JingsenZhang/Recbole-Debias/blob/master/recbole_debias/model/debiased_recommender/pda.py", "paper": "Causal Intervention for Leveraging Popularity Bias in Recommendation", "paper_link": "https://doi.org/10.1145/3404835.3462875", "authors": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON>", "ref_code": "", "repository": "RecBole-<PERSON><PERSON><PERSON>", "repo_link": "https://github.com/Jingsen<PERSON>hang/Recbole-Debias"}, {"category": "Debiased recommendation", "cate_link": "https://github.com/RUCAIBox/RecBole2.0#recbole-debias", "year": "2021", "pub": "SIGKDD'21", "model": "MACR", "model_link": "https://github.com/JingsenZhang/Recbole-Debias/blob/master/recbole_debias/model/debiased_recommender/macr.py", "paper": "Model-Agnostic Counterfactual Reasoning for Eliminating Popularity Bias in Recommender System", "paper_link": "https://doi.org/10.1145/3447548.3467289", "authors": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>feng Yi and Xi<PERSON><PERSON>", "ref_code": "https://github.com/weitianxin/MACR", "repository": "RecBole-<PERSON><PERSON><PERSON>", "repo_link": "https://github.com/Jingsen<PERSON>hang/Recbole-Debias"}, {"category": "Debiased recommendation", "cate_link": "https://github.com/RUCAIBox/RecBole2.0#recbole-debias", "year": "2021", "pub": "WWW'21", "model": "DICE", "model_link": "https://github.com/JingsenZhang/Recbole-Debias/blob/master/recbole_debias/model/debiased_recommender/dice.py", "paper": "Disentangling User Interest and Conformity for Recommendation with Causal Embedding", "paper_link": "https://doi.org/10.1145/3442381.3449788", "authors": "<PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON> and <PERSON><PERSON><PERSON>", "ref_code": "https://github.com/tsinghua-fib-lab/DICE", "repository": "RecBole-<PERSON><PERSON><PERSON>", "repo_link": "https://github.com/Jingsen<PERSON>hang/Recbole-Debias"}, {"category": "Debiased recommendation", "cate_link": "https://github.com/RUCAIBox/RecBole2.0#recbole-debias", "year": "2018", "pub": "RecSys'18", "model": "CausE", "model_link": "https://github.com/JingsenZhang/Recbole-Debias/blob/master/recbole_debias/model/debiased_recommender/cause.py", "paper": "Causal Embeddings for Recommendation", "paper_link": "https://doi.org/10.1145/3240323.3240360", "authors": "<PERSON> and <PERSON><PERSON><PERSON>", "ref_code": "", "repository": "RecBole-<PERSON><PERSON><PERSON>", "repo_link": "https://github.com/Jingsen<PERSON>hang/Recbole-Debias"}, {"category": "Debiased recommendation", "cate_link": "https://github.com/RUCAIBox/RecBole2.0#recbole-debias", "year": "2020", "pub": "WSDM'20", "model": "Rel-MF", "model_link": "https://github.com/JingsenZhang/Recbole-Debias/blob/master/recbole_debias/model/debiased_recommender/rel_mf.py", "paper": "Unbiased Recommender Learning from Missing-Not-At-Random Implicit Feedback", "paper_link": "https://doi.org/10.1145/3336191.3371783", "authors": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>", "ref_code": "", "repository": "RecBole-<PERSON><PERSON><PERSON>", "repo_link": "https://github.com/Jingsen<PERSON>hang/Recbole-Debias"}, {"category": "Fairness-aware recommendation", "cate_link": "https://github.com/RUCAIBox/RecBole2.0#recbole-fairrec", "year": "2017", "pub": "NIPS'17", "model": "FOCF", "model_link": "https://github.com/TangJiakai/RecBole-FairRec/blob/master/recbole/model/fair_recommender/focf.py", "paper": "Beyond Parity: Fairness Objectives for Collaborative Filtering", "paper_link": "https://doi.org/10.1145/3336191.3371783", "authors": "<PERSON><PERSON> and <PERSON>", "ref_code": "", "repository": "RecBole-FairRec", "repo_link": "https://github.com/TangJiakai/RecBole-FairRec"}, {"category": "Fairness-aware recommendation", "cate_link": "https://github.com/RUCAIBox/RecBole2.0#recbole-fairrec", "year": "2021", "pub": "SIGIR'21", "model": "PFCN", "model_link": "https://github.com/TangJiakai/RecBole-FairRec/blob/master/recbole/model/fair_recommender/pfcn_mlp.py", "paper": "Towards Personalized Fairness based on Causal Notion", "paper_link": "https://doi.org/10.1145/3404835.3462966", "authors": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON>", "ref_code": "https://github.com/yunqi-li/Personalized-Counterfactual-Fairness-in-Recommendation", "repository": "RecBole-FairRec", "repo_link": "https://github.com/TangJiakai/RecBole-FairRec"}, {"category": "Fairness-aware recommendation", "cate_link": "https://github.com/RUCAIBox/RecBole2.0#recbole-fairrec", "year": "2021", "pub": "WWW'21", "model": "FairGo", "model_link": "https://github.com/TangJiakai/RecBole-FairRec/blob/master/recbole/model/fair_recommender/fairgo_pmf.py", "paper": "Learning Fair Representations for Recommendation: A Graph-based Perspective", "paper_link": "https://doi.org/10.1145/3442381.3450015", "authors": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON>", "ref_code": "https://github.com/newlei/FairGo", "repository": "RecBole-FairRec", "repo_link": "https://github.com/TangJiakai/RecBole-FairRec"}, {"category": "Cross-domain recommendation", "cate_link": "https://github.com/RUCAIBox/RecBole2.0#recbole-cdr", "year": "2008", "pub": "SIGKDD'08", "model": "CMF", "model_link": "https://github.com/RUCAIBox/RecBole-CDR/blob/main/recbole_cdr/model/cross_domain_recommender/cmf.py", "paper": "Relational Learning via Collective Matrix Factorization", "paper_link": "https://doi.org/10.1145/1401890.1401969", "authors": "<PERSON><PERSON> and <PERSON>", "ref_code": "", "repository": "RecBole-CDR", "repo_link": "https://github.com/RUCAIBox/RecBole-CDR"}, {"category": "Cross-domain recommendation", "cate_link": "https://github.com/RUCAIBox/RecBole2.0#recbole-cdr", "year": "2019", "pub": "CIKM'19", "model": "DTCDR", "model_link": "https://github.com/RUCAIBox/RecBole-CDR/blob/main/recbole_cdr/model/cross_domain_recommender/dtcdr.py", "paper": "DTCDR: A Framework for Dual-Target Cross-Domain Recommendation", "paper_link": "https://doi.org/10.1145/3357384.3357992", "authors": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON>", "ref_code": "https://github.com/<PERSON><PERSON><PERSON>-<PERSON>/GA-DTCDR", "repository": "RecBole-CDR", "repo_link": "https://github.com/RUCAIBox/RecBole-CDR"}, {"category": "Cross-domain recommendation", "cate_link": "https://github.com/RUCAIBox/RecBole2.0#recbole-cdr", "year": "2018", "pub": "CIKM'18", "model": "CoNet", "model_link": "https://github.com/RUCAIBox/RecBole-CDR/blob/main/recbole_cdr/model/cross_domain_recommender/conet.py", "paper": "CoNet: Collaborative Cross Networks for Cross-Domain Recommendation", "paper_link": "https://doi.org/10.1145/3269206.3271684", "authors": "<PERSON><PERSON><PERSON><PERSON>, <PERSON> and <PERSON><PERSON>", "ref_code": "https://github.com/filipemulonde/CoNet", "repository": "RecBole-CDR", "repo_link": "https://github.com/RUCAIBox/RecBole-CDR"}, {"category": "Cross-domain recommendation", "cate_link": "https://github.com/RUCAIBox/RecBole2.0#recbole-cdr", "year": "2020", "pub": "CIKM'20", "model": "BiTGCF", "model_link": "https://github.com/RUCAIBox/RecBole-CDR/blob/main/recbole_cdr/model/cross_domain_recommender/bitgcf.py", "paper": "Cross Domain Recommendation via Bi-directional Transfer Graph Collaborative Filtering Networks", "paper_link": "https://doi.org/10.1145/3340531.3412012", "authors": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON>", "ref_code": "https://github.com/sunshinelium/Bi-TGCF", "repository": "RecBole-CDR", "repo_link": "https://github.com/RUCAIBox/RecBole-CDR"}, {"category": "Cross-domain recommendation", "cate_link": "https://github.com/RUCAIBox/RecBole2.0#recbole-cdr", "year": "2013", "pub": "PKDD'13", "model": "CLFM", "model_link": "https://github.com/RUCAIBox/RecBole-CDR/blob/main/recbole_cdr/model/cross_domain_recommender/clfm.py", "paper": "Cross-Domain Recommendation via Cluster-Level Latent Factor Model", "paper_link": "https://link.springer.com/chapter/10.1007/978-3-642-40991-2_11", "authors": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON> and <PERSON>", "ref_code": "", "repository": "RecBole-CDR", "repo_link": "https://github.com/RUCAIBox/RecBole-CDR"}, {"category": "Cross-domain recommendation", "cate_link": "https://github.com/RUCAIBox/RecBole2.0#recbole-cdr", "year": "2019", "pub": "IJCAI'19", "model": "DeepAPF", "model_link": "https://github.com/RUCAIBox/RecBole-CDR/blob/main/recbole_cdr/model/cross_domain_recommender/deepapf.py", "paper": "DeepAPF: Deep Attentive Probabilistic Factorization for Multi-site Video Recommendation", "paper_link": "https://www.ijcai.org/Proceedings/2019/0202.pdf", "authors": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> and <PERSON><PERSON><PERSON>", "ref_code": "", "repository": "RecBole-CDR", "repo_link": "https://github.com/RUCAIBox/RecBole-CDR"}, {"category": "Cross-domain recommendation", "cate_link": "https://github.com/RUCAIBox/RecBole2.0#recbole-cdr", "year": "2019", "pub": "WWW'19", "model": "NATR", "model_link": "https://github.com/RUCAIBox/RecBole-CDR/blob/main/recbole_cdr/model/cross_domain_recommender/natr.py", "paper": "Cross-domain Recommendation Without Sharing User-relevant Data", "paper_link": "https://doi.org/10.1145/3308558.3313538", "authors": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON> and <PERSON><PERSON><PERSON>", "ref_code": "", "repository": "RecBole-CDR", "repo_link": "https://github.com/RUCAIBox/RecBole-CDR"}, {"category": "Cross-domain recommendation", "cate_link": "https://github.com/RUCAIBox/RecBole2.0#recbole-cdr", "year": "2017", "pub": "IJCAI'17", "model": "EMCDR", "model_link": "https://github.com/RUCAIBox/RecBole-CDR/blob/main/recbole_cdr/model/cross_domain_recommender/emcdr.py", "paper": "Cross-Domain Recommendation: An Embedding and Mapping Approach", "paper_link": "https://static.aminer.cn/upload/pdf/program/59ae3c262bbe271c4c71f007_0.pdf", "authors": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON>", "ref_code": "", "repository": "RecBole-CDR", "repo_link": "https://github.com/RUCAIBox/RecBole-CDR"}, {"category": "Cross-domain recommendation", "cate_link": "https://github.com/RUCAIBox/RecBole2.0#recbole-cdr", "year": "2019", "pub": "CIKM'19", "model": "SSCDR", "model_link": "https://github.com/RUCAIBox/RecBole-CDR/blob/main/recbole_cdr/model/cross_domain_recommender/sscdr.py", "paper": "Semi-Supervised Learning for Cross-Domain Recommendation to Cold-Start Users", "paper_link": "https://doi.org/10.1145/3357384.3357914", "authors": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON>", "ref_code": "", "repository": "RecBole-CDR", "repo_link": "https://github.com/RUCAIBox/RecBole-CDR"}, {"category": "Cross-domain recommendation", "cate_link": "https://github.com/RUCAIBox/RecBole2.0#recbole-cdr", "year": "2018", "pub": "IJCAI'18", "model": "DCDCSR", "model_link": "https://github.com/RUCAIBox/RecBole-CDR/blob/main/recbole_cdr/model/cross_domain_recommender/dcdcsr.py", "paper": "A Deep Framework for Cross-Domain and Cross-System Recommendations", "paper_link": "https://www.ijcai.org/proceedings/2018/0516.pdf", "authors": "<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON>", "ref_code": "", "repository": "RecBole-CDR", "repo_link": "https://github.com/RUCAIBox/RecBole-CDR"}, {"category": "Graph-based recommendation", "cate_link": "https://github.com/RUCAIBox/RecBole2.0#recbole-gnn", "year": "2019", "pub": "SIGIR'19", "model": "NGCF", "model_link": "https://github.com/RUCAIBox/RecBole-GNN/blob/main/recbole_gnn/model/general_recommender/ngcf.py", "paper": "A Deep Framework for Cross-Domain and Cross-System Recommendations", "paper_link": "https://doi.org/10.1145/3331184.3331267", "authors": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON>", "ref_code": "https://github.com/xiangwang1223/neural_graph_collaborative_filtering", "repository": "RecBole-GNN", "repo_link": "https://github.com/RUCAIBox/RecBole-GNN"}, {"category": "Graph-based recommendation", "cate_link": "https://github.com/RUCAIBox/RecBole2.0#recbole-gnn", "year": "2020", "pub": "SIGIR'20", "model": "LightGCN", "model_link": "https://github.com/RUCAIBox/RecBole-GNN/blob/main/recbole_gnn/model/general_recommender/lightgcn.py", "paper": "LightGCN: Simplifying and Powering Graph Convolution Network for Recommendation", "paper_link": "https://doi.org/10.1145/3397271.3401063", "authors": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON>", "ref_code": "https://github.com/kuandeng/LightGCN", "repository": "RecBole-GNN", "repo_link": "https://github.com/RUCAIBox/RecBole-GNN"}, {"category": "Graph-based recommendation", "cate_link": "https://github.com/RUCAIBox/RecBole2.0#recbole-gnn", "year": "2021", "pub": "SIGIR'21", "model": "SGL", "model_link": "https://github.com/RUCAIBox/RecBole-GNN/blob/main/recbole_gnn/model/general_recommender/sgl.py", "paper": "Self-supervised Graph Learning for Recommendation", "paper_link": "https://doi.org/10.1145/3404835.3462862", "authors": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON>", "ref_code": "https://github.com/wujcan/SGL", "repository": "RecBole-GNN", "repo_link": "https://github.com/RUCAIBox/RecBole-GNN"}, {"category": "Graph-based recommendation", "cate_link": "https://github.com/RUCAIBox/RecBole2.0#recbole-gnn", "year": "2022", "pub": "WSDM'22", "model": "HMLET", "model_link": "https://github.com/RUCAIBox/RecBole-GNN/blob/main/recbole_gnn/model/general_recommender/hmlet.py", "paper": "Linear, or Non-Linear, That is the Question!", "paper_link": "https://doi.org/10.1145/3488560.3498501", "authors": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>", "ref_code": "https://github.com/qbxlvnf11/HMLET", "repository": "RecBole-GNN", "repo_link": "https://github.com/RUCAIBox/RecBole-GNN"}, {"category": "Graph-based recommendation", "cate_link": "https://github.com/RUCAIBox/RecBole2.0#recbole-gnn", "year": "2022", "pub": "WWW'22", "model": "NCL", "model_link": "https://github.com/RUCAIBox/RecBole-GNN/blob/main/recbole_gnn/model/general_recommender/ncl.py", "paper": "Improving Graph Collaborative Filtering with Neighborhood-enriched Contrastive Learning", "paper_link": "https://doi.org/10.1145/3485447.3512104", "authors": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON>", "ref_code": "https://github.com/RUCAIBox/NCL", "repository": "RecBole-GNN", "repo_link": "https://github.com/RUCAIBox/RecBole-GNN"}, {"category": "Graph-based recommendation", "cate_link": "https://github.com/RUCAIBox/RecBole2.0#recbole-gnn", "year": "2022", "pub": "SIGIR'22", "model": "SimGCL", "model_link": "https://github.com/RUCAIBox/RecBole-GNN/blob/main/recbole_gnn/model/general_recommender/simgcl.py", "paper": "Are Graph Augmentations Necessary? Simple Graph Contrastive Learning for Recommendation", "paper_link": "https://doi.org/10.1145/3477495.3531937", "authors": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON>", "ref_code": "https://github.com/Coder-Yu/QRec", "repository": "RecBole-GNN", "repo_link": "https://github.com/RUCAIBox/RecBole-GNN"}, {"category": "Graph-based recommendation", "cate_link": "https://github.com/RUCAIBox/RecBole2.0#recbole-gnn", "year": "2019", "pub": "AAAI'19", "model": "SR-GNN", "model_link": "https://github.com/RUCAIBox/RecBole-GNN/blob/main/recbole_gnn/model/sequential_recommender/srgnn.py", "paper": "Session-based Recommendation with Graph Neural Networks", "paper_link": "https://ojs.aaai.org/index.php/AAAI/article/view/3804", "authors": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON>", "ref_code": "https://github.com/CRIPAC-DIG/SR-GNN", "repository": "RecBole-GNN", "repo_link": "https://github.com/RUCAIBox/RecBole-GNN"}, {"category": "Graph-based recommendation", "cate_link": "https://github.com/RUCAIBox/RecBole2.0#recbole-gnn", "year": "2019", "pub": "IJCAI'19", "model": "GCSAN", "model_link": "https://github.com/RUCAIBox/RecBole-GNN/blob/main/recbole_gnn/model/sequential_recommender/gcsan.py", "paper": "Graph Contextualized Self-Attention Network for Session-based Recommendation", "paper_link": "https://www.ijcai.org/proceedings/2019/0547.pdf", "authors": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON>", "ref_code": "", "repository": "RecBole-GNN", "repo_link": "https://github.com/RUCAIBox/RecBole-GNN"}, {"category": "Graph-based recommendation", "cate_link": "https://github.com/RUCAIBox/RecBole2.0#recbole-gnn", "year": "2019", "pub": "CIKM'19", "model": "NISER+", "model_link": "https://github.com/RUCAIBox/RecBole-GNN/blob/main/recbole_gnn/model/sequential_recommender/niser.py", "paper": "NISER: Normalized Item and Session Representations to Handle Popularity Bias", "paper_link": "https://arxiv.org/abs/1909.04276", "authors": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>", "ref_code": "https://github.com/johnny12150/NISER", "repository": "RecBole-GNN", "repo_link": "https://github.com/RUCAIBox/RecBole-GNN"}, {"category": "Graph-based recommendation", "cate_link": "https://github.com/RUCAIBox/RecBole2.0#recbole-gnn", "year": "2020", "pub": "SIGKDD'20", "model": "LESSR", "model_link": "https://github.com/RUCAIBox/RecBole-GNN/blob/main/recbole_gnn/model/sequential_recommender/lessr.py", "paper": "Handling Information Loss of Graph Neural Networks for Session-based Recommendation", "paper_link": "https://doi.org/10.1145/3394486.3403170", "authors": "<PERSON><PERSON><PERSON> and <PERSON>", "ref_code": "https://github.com/twchen/lessr", "repository": "RecBole-GNN", "repo_link": "https://github.com/RUCAIBox/RecBole-GNN"}, {"category": "Graph-based recommendation", "cate_link": "https://github.com/RUCAIBox/RecBole2.0#recbole-gnn", "year": "2020", "pub": "SIGIR'20", "model": "TAGNN", "model_link": "https://github.com/RUCAIBox/RecBole-GNN/blob/main/recbole_gnn/model/sequential_recommender/tagnn.py", "paper": "TAGNN: Target Attentive Graph Neural Networks for Session-based Recommendation", "paper_link": "https://doi.org/10.1145/3397271.3401319", "authors": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON> and <PERSON><PERSON><PERSON>", "ref_code": "https://github.com/CRIPAC-DIG/TAGNN", "repository": "RecBole-GNN", "repo_link": "https://github.com/RUCAIBox/RecBole-GNN"}, {"category": "Graph-based recommendation", "cate_link": "https://github.com/RUCAIBox/RecBole2.0#recbole-gnn", "year": "2020", "pub": "SIGIR'20", "model": "GCE-GNN", "model_link": "https://github.com/RUCAIBox/RecBole-GNN/blob/main/recbole_gnn/model/sequential_recommender/gcegnn.py", "paper": "Global Context Enhanced Graph Neural Networks for Session-based Recommendation", "paper_link": "https://doi.org/10.1145/3397271.3401142", "authors": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON>", "ref_code": "https://github.com/CCIIPLab/GCE-GNN", "repository": "RecBole-GNN", "repo_link": "https://github.com/RUCAIBox/RecBole-GNN"}, {"category": "Graph-based recommendation", "cate_link": "https://github.com/RUCAIBox/RecBole2.0#recbole-gnn", "year": "2020", "pub": "CIKM'20", "model": "SGNN-HN", "model_link": "https://github.com/RUCAIBox/RecBole-GNN/blob/main/recbole_gnn/model/sequential_recommender/sgnnhn.py", "paper": "Star Graph Neural Networks for Session-based Recommendation", "paper_link": "https://doi.org/10.1145/3340531.3412014", "authors": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON>", "ref_code": "https://bitbucket.org/nudtpanzq/sgnn-hn", "repository": "RecBole-GNN", "repo_link": "https://github.com/RUCAIBox/RecBole-GNN"}, {"category": "Graph-based recommendation", "cate_link": "https://github.com/RUCAIBox/RecBole2.0#recbole-gnn", "year": "2019", "pub": "SIGIR'19", "model": "DiffNet", "model_link": "https://github.com/RUCAIBox/RecBole-GNN/blob/main/recbole_gnn/model/social_recommender/diffnet.py", "paper": "A Neural Influence Diffusion Model for Social Recommendation", "paper_link": "https://doi.org/10.1145/3331184.3331214", "authors": "<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON>", "ref_code": "https://github.com/PeiJieSun/diffnet", "repository": "RecBole-GNN", "repo_link": "https://github.com/RUCAIBox/RecBole-GNN"}, {"category": "Graph-based recommendation", "cate_link": "https://github.com/RUCAIBox/RecBole2.0#recbole-gnn", "year": "2021", "pub": "WWW'21", "model": "MHCN", "model_link": "https://github.com/RUCAIBox/RecBole-GNN/blob/main/recbole_gnn/model/social_recommender/mhcn.py", "paper": "Self-Supervised Multi-Channel Hypergraph Convolutional Network for Social Recommendation", "paper_link": "https://doi.org/10.1145/3442381.3449844", "authors": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>", "ref_code": "https://github.com/Coder-Yu/QRec", "repository": "RecBole-GNN", "repo_link": "https://github.com/RUCAIBox/RecBole-GNN"}, {"category": "Graph-based recommendation", "cate_link": "https://github.com/RUCAIBox/RecBole2.0#recbole-gnn", "year": "2021", "pub": "SIGKDD'21", "model": "SEPT", "model_link": "https://github.com/RUCAIBox/RecBole-GNN/blob/main/recbole_gnn/model/social_recommender/sept.py", "paper": "Socially-Aware Self-Supervised Tri-Training for Recommendation", "paper_link": "https://doi.org/10.1145/3447548.3467340", "authors": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>", "ref_code": "https://github.com/Coder-Yu/QRec", "repository": "RecBole-GNN", "repo_link": "https://github.com/RUCAIBox/RecBole-GNN"}, {"category": "Transformer-based Recommendation", "cate_link": "https://github.com/RUCAIBox/RecBole2.0#recbole-trm", "year": "2020", "pub": "WSDM'20", "model": "TiSASRec", "model_link": "https://github.com/RUCAIBox/RecBole-TRM/blob/main/recbole/model/transformer_recommender/tisasrec.py", "paper": "Time Interval Aware Self-Attention for Sequential Recommendation", "paper_link": "https://doi.org/10.1145/3336191.3371786", "authors": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON>", "ref_code": "https://github.com/JiachengLi1995/TiSASRec", "repository": "RecBole-TRM", "repo_link": "https://github.com/RUCAIBox/RecBole-TRM"}, {"category": "Transformer-based Recommendation", "cate_link": "https://github.com/RUCAIBox/RecBole2.0#recbole-trm", "year": "2020", "pub": "RecSys'20", "model": "SSE-PT", "model_link": "https://github.com/RUCAIBox/RecBole-TRM/blob/main/recbole/model/transformer_recommender/ssept.py", "paper": "SSE-PT: Sequential Recommendation Via Personalized Transformer", "paper_link": "https://doi.org/10.1145/3383313.3412258", "authors": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> and <PERSON>", "ref_code": "https://github.com/wuliwei9278/SSE-PT", "repository": "RecBole-TRM", "repo_link": "https://github.com/RUCAIBox/RecBole-TRM"}, {"category": "Transformer-based Recommendation", "cate_link": "https://github.com/RUCAIBox/RecBole2.0#recbole-trm", "year": "2021", "pub": "SIGIR'21", "model": "LightSANs", "model_link": "https://github.com/RUCAIBox/RecBole-TRM/blob/main/recbole/model/sequential_recommender/lightsans.py", "paper": "Lighter and Better: Low-Rank Decomposed Self-Attention Networks for Next-Item Recommendation", "paper_link": "https://doi.org/10.1145/3404835.3462978", "authors": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>", "ref_code": "https://github.com/BELIEVEfxy/LightSANs", "repository": "RecBole-TRM", "repo_link": "https://github.com/RUCAIBox/RecBole-TRM"}, {"category": "Transformer-based Recommendation", "cate_link": "https://github.com/RUCAIBox/RecBole2.0#recbole-trm", "year": "2021", "pub": "NIPS'21", "model": "gMLP", "model_link": "https://github.com/RUCAIBox/RecBole-TRM/blob/main/recbole/model/transformer_recommender/gmlp.py", "paper": "Pay Attention to MLPs", "paper_link": "https://proceedings.neurips.cc/paper/2021/hash/4cc05b35c2f937c5bd9e7d41d3686fff-Abstract.html", "authors": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON> and <PERSON><PERSON><PERSON> <PERSON>", "ref_code": "https://github.com/jaketae/g-mlp", "repository": "RecBole-TRM", "repo_link": "https://github.com/RUCAIBox/RecBole-TRM"}, {"category": "Transformer-based Recommendation", "cate_link": "https://github.com/RUCAIBox/RecBole2.0#recbole-trm", "year": "2022", "pub": "SIGIR'22", "model": "CORE", "model_link": "https://github.com/RUCAIBox/RecBole-TRM/blob/main/recbole/model/sequential_recommender/core.py", "paper": "CORE: Simple and Effective Session-based Recommendation within Consistent Representation Space", "paper_link": "https://arxiv.org/abs/2204.11067", "authors": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> and <PERSON>", "ref_code": "https://github.com/RUCAIBox/CORE", "repository": "RecBole-TRM", "repo_link": "https://github.com/RUCAIBox/RecBole-TRM"}, {"category": "Transformer-based Recommendation", "cate_link": "https://github.com/RUCAIBox/RecBole2.0#recbole-trm", "year": "2019", "pub": "EMNLP/IJCNLP'19", "model": "NRMS", "model_link": "https://github.com/RUCAIBox/RecBole-TRM/blob/main/recbole_trm/model/nrms.py", "paper": "Neural News Recommendation with Multi-Head Self-Attention", "paper_link": "https://aclanthology.org/D19-1671.pdf", "authors": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON> and <PERSON><PERSON>", "ref_code": "https://github.com/wuch15/EMNLP2019-NRMS", "repository": "RecBole-TRM", "repo_link": "https://github.com/RUCAIBox/RecBole-TRM"}, {"category": "Transformer-based Recommendation", "cate_link": "https://github.com/RUCAIBox/RecBole2.0#recbole-trm", "year": "2019", "pub": "IJCAI'19", "model": "NAML", "model_link": "https://github.com/RUCAIBox/RecBole-TRM/blob/main/recbole_trm/model/naml.py", "paper": "Neural News Recommendation with Attentive Multi-View Learning", "paper_link": "https://doi.org/10.5555/3367471.3367578", "authors": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON>", "ref_code": "https://github.com/wuch15/IJCAI2019-NAML", "repository": "RecBole-TRM", "repo_link": "https://github.com/RUCAIBox/RecBole-TRM"}, {"category": "Person-job Fit", "cate_link": "https://github.com/RUCAIBox/RecBole2.0#recbole-pjf", "year": "2009", "pub": "UAI'09", "model": "BPR", "model_link": "https://github.com/RUCAIBox/RecBole-PJF/blob/main/recbole/model/general_recommender/bpr.py", "paper": "BPR: Bayesian Personalized Ranking from Implicit Feedback", "paper_link": "https://arxiv.org/abs/1205.2618", "authors": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON> and <PERSON>", "ref_code": "https://github.com/RUCAIBox/RecBole", "repository": "RecBole-PJF", "repo_link": "https://github.com/RUCAIBox/RecBole-PJF"}, {"category": "Person-job Fit", "cate_link": "https://github.com/RUCAIBox/RecBole2.0#recbole-pjf", "year": "2017", "pub": "WWW'17", "model": "NeuMF", "model_link": "https://github.com/RUCAIBox/RecBole-PJF/blob/main/recbole/model/general_recommender/neumf.py", "paper": "Neural Collaborative Filtering", "paper_link": "https://doi.org/10.1145/3038912.3052569", "authors": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON>", "ref_code": "https://github.com/hexiangnan/neural_collaborative_filtering", "repository": "RecBole-PJF", "repo_link": "https://github.com/RUCAIBox/RecBole-PJF"}, {"category": "Person-job Fit", "cate_link": "https://github.com/RUCAIBox/RecBole2.0#recbole-pjf", "year": "2020", "pub": "SIGIR'20", "model": "LightGCN", "model_link": "https://github.com/RUCAIBox/RecBole-PJF/blob/main/recbole/model/general_recommender/lightgcn.py", "paper": "LightGCN: Simplifying and Powering Graph Convolution Network for Recommendation", "paper_link": "https://doi.org/10.1145/3397271.3401063", "authors": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON>", "ref_code": "https://github.com/kuandeng/LightGCN", "repository": "RecBole-PJF", "repo_link": "https://github.com/RUCAIBox/RecBole-PJF"}, {"category": "Person-job Fit", "cate_link": "https://github.com/RUCAIBox/RecBole2.0#recbole-pjf", "year": "2019", "pub": "RecSys'19", "model": "LFRR", "model_link": "https://github.com/RUCAIBox/RecBole-PJF/blob/main/recbole_pjf/model/lfrr.py", "paper": "Latent Factor Models and Aggregation Operators for Collaborative Filtering in Reciprocal Recommender systems", "paper_link": "https://doi.org/10.1145/3298689.3347026", "authors": "<PERSON> and <PERSON>", "ref_code": "", "repository": "RecBole-PJF", "repo_link": "https://github.com/RUCAIBox/RecBole-PJF"}, {"category": "Person-job Fit", "cate_link": "https://github.com/RUCAIBox/RecBole2.0#recbole-pjf", "year": "2018", "pub": "TMIS'18", "model": "PJFNN", "model_link": "https://github.com/RUCAIBox/RecBole-PJF/blob/main/recbole_pjf/model/pjfnn.py", "paper": "Person-Job fit: Adapting the Right Talent for the Right Job with Joint Representation Learning", "paper_link": "https://doi.org/10.1145/3234465", "authors": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON> and <PERSON>", "ref_code": "", "repository": "RecBole-PJF", "repo_link": "https://github.com/RUCAIBox/RecBole-PJF"}, {"category": "Person-job Fit", "cate_link": "https://github.com/RUCAIBox/RecBole2.0#recbole-pjf", "year": "2018", "pub": "SIGIR'18", "model": "BPJFNN", "model_link": "https://github.com/RUCAIBox/RecBole-PJF/blob/main/recbole_pjf/model/BPJFNN.py", "paper": "Enhancing Person-Job Fit for Talent Recruitment: An Ability-aware Neural Network Approach", "paper_link": "https://doi.org/10.1145/3209978.3210025", "authors": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON> and <PERSON>", "ref_code": "", "repository": "RecBole-PJF", "repo_link": "https://github.com/RUCAIBox/RecBole-PJF"}, {"category": "Person-job Fit", "cate_link": "https://github.com/RUCAIBox/RecBole2.0#recbole-pjf", "year": "2018", "pub": "SIGIR'18", "model": "APJFNN", "model_link": "https://github.com/RUCAIBox/RecBole-PJF/blob/main/recbole_pjf/model/apjfnn.py", "paper": "Enhancing Person-Job Fit for Talent Recruitment: An Ability-aware Neural Network Approach", "paper_link": "https://doi.org/10.1145/3209978.3210025", "authors": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON> and <PERSON>", "ref_code": "", "repository": "RecBole-PJF", "repo_link": "https://github.com/RUCAIBox/RecBole-PJF"}, {"category": "Person-job Fit", "cate_link": "https://github.com/RUCAIBox/RecBole2.0#recbole-pjf", "year": "-", "pub": "-", "model": "BERT", "model_link": "https://github.com/RUCAIBox/RecBole-PJF/blob/main/recbole_pjf/model/bert.py", "paper": "A twin tower model with a text encoder using BERT", "paper_link": "", "authors": "", "ref_code": "", "repository": "RecBole-PJF", "repo_link": "https://github.com/RUCAIBox/RecBole-PJF"}, {"category": "Person-job Fit", "cate_link": "https://github.com/RUCAIBox/RecBole2.0#recbole-pjf", "year": "2019", "pub": "CIKM'19", "model": "IPJF", "model_link": "https://github.com/RUCAIBox/RecBole-PJF/blob/main/recbole_pjf/model/ipjf.py", "paper": "Towards Effective and Interpretable Person-Job Fitting", "paper_link": "https://doi.org/10.1145/3357384.3357949", "authors": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON> and <PERSON><PERSON>", "ref_code": "", "repository": "RecBole-PJF", "repo_link": "https://github.com/RUCAIBox/RecBole-PJF"}, {"category": "Person-job Fit", "cate_link": "https://github.com/RUCAIBox/RecBole2.0#recbole-pjf", "year": "2020", "pub": "CIKM'20", "model": "PJFFF", "model_link": "https://github.com/RUCAIBox/RecBole-PJF/blob/main/recbole_pjf/model/pjfff.py", "paper": "Learning Effective Representations for Person-Job Fit by Feature Fusion", "paper_link": "https://doi.org/10.1145/3340531.3412717", "authors": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON>", "ref_code": "", "repository": "RecBole-PJF", "repo_link": "https://github.com/RUCAIBox/RecBole-PJF"}, {"category": "Person-job Fit", "cate_link": "https://github.com/RUCAIBox/RecBole2.0#recbole-pjf", "year": "2022", "pub": "DASFAA'22", "model": "SHPJF", "model_link": "https://github.com/RUCAIBox/RecBole-PJF/blob/main/recbole_pjf/model/shpjf.py", "paper": "Leveraging Search History for Improving Person-Job <PERSON>t", "paper_link": "https://link.springer.com/chapter/10.1007/978-3-031-00123-9_3", "authors": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON> and <PERSON><PERSON><PERSON><PERSON>", "ref_code": "", "repository": "RecBole-PJF", "repo_link": "https://github.com/RUCAIBox/RecBole-PJF"}, {"category": "General Recommendation", "year": "2020", "pub": "SIGIR'20", "model": "LightGCN", "model_link": "/docs/user_guide/model/general/lightgcn.html", "paper": "LightGCN: Simplifying and Powering Graph Convolution Network for Recommendation", "paper_link": "https://doi.org/10.1145/3397271.3401063", "authors": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON>", "ref_code": "https://github.com/kuandeng/LightGCN", "repository": "RecBole", "repo_link": "https://github.com/RUCAIBox/RecBole"}, {"category": "Knowledge-aware Recommendation", "year": "2018", "pub": "MDPI'18", "model": "CFKG", "model_link": "/docs/user_guide/model/knowledge/cfkg.html", "paper": "Learning heterogeneous knowledge base embeddings for explainable recommendation", "paper_link": "https://doi.org/10.3390/a11090137", "authors": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>", "ref_code": "", "repository": "RecBole", "repo_link": "https://github.com/RUCAIBox/RecBole"}, {"category": "Knowledge-aware Recommendation", "year": "2016", "pub": "SIGKDD'16", "model": "CKE", "model_link": "/docs/user_guide/model/knowledge/cke.html", "paper": "Collaborative Knowledge Base Embedding for Recommender Systems", "paper_link": "https://doi.org/10.1145/2939672.2939673", "authors": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>", "ref_code": "", "repository": "RecBole", "repo_link": "https://github.com/RUCAIBox/RecBole"}, {"category": "Knowledge-aware Recommendation", "year": "2019", "pub": "SIGKDD'19", "model": "KGAT", "model_link": "/docs/user_guide/model/knowledge/kgat.html", "paper": "KGAT: Knowledge Graph Attention Network for Recommendation", "paper_link": "https://doi.org/10.1145/3292500.3330989", "authors": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON> ", "ref_code": "https://github.com/xiangwang1223/knowledge_graph_attention_network", "repository": "RecBole", "repo_link": "https://github.com/RUCAIBox/RecBole"}, {"category": "Knowledge-aware Recommendation", "year": "2019", "pub": "WWW'19", "model": "KGCN", "model_link": "/docs/user_guide/model/knowledge/kgcn.html", "paper": "Knowledge graph convolution networks for recommender systems", "paper_link": "https://doi.org/10.1145/3308558.3313417", "authors": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>", "ref_code": "https://github.com/hwwang55/KGCN", "repository": "RecBole", "repo_link": "https://github.com/RUCAIBox/RecBole"}, {"category": "Knowledge-aware Recommendation", "year": "2019", "pub": "SIGKDD'19", "model": "KGNN-LS", "model_link": "/docs/user_guide/model/knowledge/kgnnls.html", "paper": "Knowledge-aware Graph Neural Networks with Label Smoothness Regularization for Recommender Systems", "paper_link": "https://doi.org/10.48550/arXiv.1905.04413", "authors": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>", "ref_code": "https://github.com/hwwang55/KGNN-LS", "repository": "RecBole", "repo_link": "https://github.com/RUCAIBox/RecBole"}, {"category": "Knowledge-aware Recommendation", "year": "2019", "pub": "WWW'19", "model": "KTUP", "model_link": "/docs/user_guide/model/knowledge/ktup.html", "paper": "Unifying Knowledge Graph Learning and Recommendation:Towards a Better Understanding of User Preferences", "paper_link": "https://doi.org/10.1145/3308558.3313705", "authors": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>", "ref_code": "https://github.com/TaoMiner/joint-kg-recommender", "repository": "RecBole", "repo_link": "https://github.com/RUCAIBox/RecBole"}, {"category": "Knowledge-aware Recommendation", "year": "2019", "pub": "WWW'19", "model": "MKR", "model_link": "/docs/user_guide/model/knowledge/mkr.html", "paper": "Multi-Task Feature Learning for Knowledge Graph Enhanced Recommendation", "paper_link": "https://doi.org/10.48550/arXiv.1901.08907", "authors": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>", "ref_code": "https://github.com/hsientzucheng/MKR.PyTorch", "repository": "RecBole", "repo_link": "https://github.com/RUCAIBox/RecBole"}, {"category": "Knowledge-aware Recommendation", "year": "2018", "pub": "CIKM'18", "model": "RippleNet", "model_link": "/docs/user_guide/model/knowledge/ripplenet.html", "paper": "Collaborative Knowledge Base Embedding for Recommender Systems", "paper_link": "https://dl.acm.org/doi/10.1145/3269206.3271739", "authors": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>", "ref_code": "", "repository": "RecBole", "repo_link": "https://github.com/RUCAIBox/RecBole"}, {"category": "Knowledge-aware Recommendation", "year": "2022", "pub": "WWW'21", "model": "KGIN", "model_link": "/docs/user_guide/model/knowledge/kgin.html", "paper": "Learning Intents behind Interactions with Knowledge Graph for Recommendation", "paper_link": "https://dl.acm.org/doi/10.1145/3442381.3450133", "authors": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>", "ref_code": "", "repository": "RecBole", "repo_link": "https://github.com/RUCAIBox/RecBole"}, {"category": "Knowledge-aware Recommendation", "year": "2022", "pub": "SIGIR'22", "model": "MCCLK", "model_link": "/docs/user_guide/model/knowledge/mcclk.html", "paper": "Collaborative Knowledge Base Embedding for Recommender Systems", "paper_link": "https://dl.acm.org/doi/10.1145/3477495.3532025", "authors": "<PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>", "ref_code": "", "repository": "RecBole", "repo_link": "https://github.com/RUCAIBox/RecBole"}, {"category": "Context-aware Recommendation", "cate_link": "/docs/user_guide/model_intro.html#context-aware-recommendation", "year": "2023", "pub": "SIGIR'23", "model": "EulerNet", "model_link": "/docs/user_guide/model/context/eulernet.html", "paper": "EulerNet: Adaptive Feature Interaction Learning via Euler's Formula for CTR Prediction", "paper_link": "https://dl.acm.org/doi/10.1145/3539618.3591681", "authors": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>", "ref_code": "https://github.com/chenyuwuxin/EulerNet", "repository": "RecBole", "repo_link": "https://github.com/RUCAIBox/RecBole"}, {"category": "Sequential Recommendation", "cate_link": "/docs/user_guide/model_intro.html#sequential-recommendation", "year": "2023", "pub": "SIGIR'23", "model": "FEARec", "model_link": "/docs/user_guide/model/sequential/fearec.html", "paper": "FEARec: Frequency Enhanced Hybrid Attention Network for Sequential Recommendation", "paper_link": "https://dl.acm.org/doi/10.1145/3539618.3591689", "authors": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>", "ref_code": "https://github.com/sudaada/FEARec", "repository": "RecBole", "repo_link": "https://github.com/RUCAIBox/RecBole"}, {"category": "General Recommendation", "cate_link": "/docs/user_guide/model_intro.html#general-recommendation", "year": "2023", "pub": "SIGIR'23", "model": "DiffRec", "model_link": "/docs/user_guide/model/general/diffrec.html", "paper": "Diffusion Recommender Model", "paper_link": "https://dl.acm.org/doi/10.1145/3539618.3591663", "authors": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>", "ref_code": "https://github.com/YiyanXu/DiffRec", "repository": "RecBole", "repo_link": "https://github.com/RUCAIBox/RecBole"}, {"category": "General Recommendation", "cate_link": "/docs/user_guide/model_intro.html#general-recommendation", "year": "2023", "pub": "SIGIR'23", "model": "LDiffRec", "model_link": "/docs/user_guide/model/general/ldiffrec.html", "paper": "Diffusion Recommender Model", "paper_link": "https://dl.acm.org/doi/10.1145/3539618.3591663", "authors": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>", "ref_code": "https://github.com/YiyanXu/DiffRec", "repository": "RecBole", "repo_link": "https://github.com/RUCAIBox/RecBole"}, {"category": "Sequential Recommendation", "cate_link": "/docs/user_guide/model_intro.html#sequential-recommendation", "year": "2024", "pub": "WSDM'24", "model": "GRU4RecCPR", "model_link": "/docs/user_guide/model/sequential/gru4reccpr.html", "paper": "To Copy, or not to Copy; That is a Critical Issue of the Output Softmax Layer in Neural Sequential Recommenders", "paper_link": "https://dl.acm.org/doi/10.1145/3616855.3635755", "authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>", "ref_code": "https://github.com/iesl/softmax_CPR_recommend", "repository": "RecBole", "repo_link": "https://github.com/RUCAIBox/RecBole"}, {"category": "Sequential Recommendation", "cate_link": "/docs/user_guide/model_intro.html#sequential-recommendation", "year": "2024", "pub": "WSDM'24", "model": "SASRecCPR", "model_link": "/docs/user_guide/model/sequential/sasreccpr.html", "paper": "To Copy, or not to Copy; That is a Critical Issue of the Output Softmax Layer in Neural Sequential Recommenders", "paper_link": "https://dl.acm.org/doi/10.1145/3616855.3635755", "authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>", "ref_code": "https://github.com/iesl/softmax_CPR_recommend", "repository": "RecBole", "repo_link": "https://github.com/RUCAIBox/RecBole"}]}