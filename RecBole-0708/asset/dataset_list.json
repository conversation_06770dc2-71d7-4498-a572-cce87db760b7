{"data": [{"dataset": "MovieLens", "dataset_link": "https://github.com/RUCAIBox/RecDatasets/tree/master/dataset_info/MovieLens", "user_num": "-", "item_num": "-", "inter_num": "-", "sparsity": "-", "type": "Rating", "link_name": "script", "link_url": "https://github.com/RUCAIBox/RecDatasets/blob/master/conversion_tools/usage/MovieLens.md"}, {"dataset": "Anime", "dataset_link": "", "user_num": "73,515", "item_num": "11,200", "inter_num": "7,813,737", "sparsity": "99.05%", "type": "Rating [-1, 1-10]", "link_name": "script", "link_url": "https://github.com/RUCAIBox/RecDatasets/blob/master/conversion_tools/usage/Anime.md"}, {"dataset": "Epinions", "dataset_link": "", "user_num": "116,260", "item_num": "41,269", "inter_num": "188,478", "sparsity": "99.99%", "type": "Rating [1-5]", "link_name": "script", "link_url": "https://github.com/RUCAIBox/RecDatasets/blob/master/conversion_tools/usage/Epinions.md"}, {"dataset": "Yelp", "dataset_link": "https://github.com/RUCAIBox/RecommenderSystems-Datasets/tree/master/dataset_info/Yelp", "user_num": "-", "item_num": "-", "inter_num": "-", "sparsity": "-", "type": "Rating", "link_name": "script", "link_url": "https://github.com/RUCAIBox/RecDatasets/blob/master/conversion_tools/usage/Yelp.md"}, {"dataset": "Netflix", "dataset_link": "", "user_num": "480,189", "item_num": "17,770", "inter_num": "100,480,507", "sparsity": "98.82%", "type": "Rating [1-5]", "link_name": "script", "link_url": "https://github.com/RUCAIBox/RecDatasets/blob/master/conversion_tools/usage/Netflix.md"}, {"dataset": "Book-Crossing", "dataset_link": "", "user_num": "105,284", "item_num": "340,557", "inter_num": "1,149,780", "sparsity": "99.99%", "type": "Rating [0-10]", "link_name": "script", "link_url": "https://github.com/RUCAIBox/RecDatasets/blob/master/conversion_tools/usage/Book-Crossing.md"}, {"dataset": "<PERSON><PERSON>", "dataset_link": "", "user_num": "73,421", "item_num": "101", "inter_num": "4,136,360", "sparsity": "44.22%", "type": "Rating [-10, 10]", "link_name": "script", "link_url": "https://github.com/RUCAIBox/RecDatasets/blob/master/conversion_tools/usage/Jester.md"}, {"dataset": "Douban", "dataset_link": "", "user_num": "738,701", "item_num": "28", "inter_num": "2,125,056", "sparsity": "89.73%", "type": "Rating [0, 5]", "link_name": "script", "link_url": "https://github.com/RUCAIBox/RecDatasets/blob/master/conversion_tools/usage/Douban.md"}, {"dataset": "Yahoo Music", "dataset_link": "", "user_num": "1,948,882", "item_num": "98,211", "inter_num": "111,557,943", "sparsity": "99.99%", "type": "Rating [0, 100]", "link_name": "script", "link_url": "https://github.com/RUCAIBox/RecDatasets/blob/master/conversion_tools/usage/YahooMusic.md"}, {"dataset": "KDD2010", "dataset_link": "https://github.com/RUCAIBox/RecommenderSystems-Datasets/tree/master/dataset_info/KDD2010", "user_num": "-", "item_num": "-", "inter_num": "-", "sparsity": "-", "type": "Rating", "link_name": "script", "link_url": "https://github.com/RUCAIBox/RecDatasets/blob/master/conversion_tools/usage/KDD2010.md"}, {"dataset": "Amazon", "dataset_link": "https://github.com/RUCAIBox/RecommenderSystems-Datasets/tree/master/dataset_info/Amazon", "user_num": "-", "item_num": "-", "inter_num": "-", "sparsity": "-", "type": "Rating [0, 5]", "link_name": "script", "link_url": "https://github.com/RUCAIBox/RecDatasets/blob/master/conversion_tools/usage/Amazon.md"}, {"dataset": "Pinterest", "dataset_link": "", "user_num": "55,187", "item_num": "9,911", "inter_num": "1,445,622", "sparsity": "99.74%", "type": "-", "link_name": "script", "link_url": "https://github.com/RUCAIBox/RecDatasets/blob/master/conversion_tools/usage/Pinterest.md"}, {"dataset": "<PERSON><PERSON><PERSON>", "dataset_link": "https://github.com/RUCAIBox/RecommenderSystems-Datasets/tree/master/dataset_info/Gowalla", "user_num": "107,092", "item_num": "1,280,969", "inter_num": "6,442,892", "sparsity": "99.99%", "type": "Check-in", "link_name": "script", "link_url": "https://github.com/RUCAIBox/RecDatasets/blob/master/conversion_tools/usage/Gowalla.md"}, {"dataset": "Last.FM", "dataset_link": "", "user_num": "1,892", "item_num": "17,632", "inter_num": "92,834", "sparsity": "99.72%", "type": "Click", "link_name": "script", "link_url": "https://github.com/RUCAIBox/RecDatasets/blob/master/conversion_tools/usage/LastFM.md"}, {"dataset": "DIGINETICA", "dataset_link": "https://github.com/RUCAIBox/RecommenderSystems-Datasets/tree/master/dataset_info/DIGINETICA", "user_num": "204,789", "item_num": "184,047", "inter_num": "993,483", "sparsity": "99.99%", "type": "Click", "link_name": "script", "link_url": "https://github.com/RUCAIBox/RecDatasets/blob/master/conversion_tools/usage/DIGINETICA.md"}, {"dataset": "Steam", "dataset_link": "https://github.com/RUCAIBox/RecommenderSystems-Datasets/tree/master/dataset_info/Steam", "user_num": "2,567,538", "item_num": "32,135", "inter_num": "7,793,069", "sparsity": "99.99%", "type": "Buy", "link_name": "script", "link_url": "https://github.com/RUCAIBox/RecDatasets/blob/master/conversion_tools/usage/Steam.md"}, {"dataset": "Ta Feng", "dataset_link": "https://github.com/RUCAIBox/RecommenderSystems-Datasets/tree/master/dataset_info/TaFeng", "user_num": "32,266", "item_num": "23,812", "inter_num": "817,741", "sparsity": "99.89%", "type": "Click", "link_name": "script", "link_url": "https://github.com/RUCAIBox/RecDatasets/blob/master/conversion_tools/usage/TaFeng.md"}, {"dataset": "Foursquare", "dataset_link": "https://github.com/RUCAIBox/RecommenderSystems-Datasets/tree/master/dataset_info/Foursquare", "user_num": "-", "item_num": "-", "inter_num": "-", "sparsity": "-", "type": "Check-in", "link_name": "script", "link_url": "https://github.com/RUCAIBox/RecDatasets/blob/master/conversion_tools/usage/Foursquare.md"}, {"dataset": "Tmall", "dataset_link": "https://github.com/RUCAIBox/RecommenderSystems-Datasets/tree/master/dataset_info/Tmall", "user_num": "963,923", "item_num": "2,353,207", "inter_num": "44,528,127", "sparsity": "99.99%", "type": "Click/Buy", "link_name": "script", "link_url": "https://github.com/RUCAIBox/RecDatasets/blob/master/conversion_tools/usage/Tmall.md"}, {"dataset": "YOOCHOOSE", "dataset_link": "https://github.com/RUCAIBox/RecommenderSystems-Datasets/tree/master/dataset_info/YOOCHOOSE", "user_num": "9,249,729", "item_num": "52,739", "inter_num": "34,154,697", "sparsity": "99.99%", "type": "Click/Buy", "link_name": "script", "link_url": "https://github.com/RUCAIBox/RecDatasets/blob/master/conversion_tools/usage/YOOCHOOSE.md"}, {"dataset": "Retailrocket", "dataset_link": "https://github.com/RUCAIBox/RecommenderSystems-Datasets/tree/master/dataset_info/Retailrocket", "user_num": "1,407,580", "item_num": "247,085", "inter_num": "2,756,101", "sparsity": "99.99%", "type": "View/Addtocart/Transaction", "link_name": "script", "link_url": "https://github.com/RUCAIBox/RecDatasets/blob/master/conversion_tools/usage/Retailrocket.md"}, {"dataset": "LFM-1b", "dataset_link": "https://github.com/RUCAIBox/RecommenderSystems-Datasets/tree/master/dataset_info/LFM-1b", "user_num": "120,322", "item_num": "3,123,496", "inter_num": "1,088,161,692", "sparsity": "99.71%", "type": "Click", "link_name": "script", "link_url": "https://github.com/RUCAIBox/RecDatasets/blob/master/conversion_tools/usage/LFM-1b.md"}, {"dataset": "MIND", "dataset_link": "https://github.com/RUCAIBox/RecommenderSystems-Datasets/tree/master/dataset_info/MIND", "user_num": "-", "item_num": "-", "inter_num": "-", "sparsity": "-", "type": "Click", "link_name": "script", "link_url": "https://github.com/RUCAIBox/RecDatasets/blob/master/conversion_tools/usage/MIND.md"}, {"dataset": "BeerAdvocate", "dataset_link": "", "user_num": "33,388", "item_num": "66,055", "inter_num": "1,586,614", "sparsity": "99.9281%", "type": "Rating [0, 5]", "link_name": "link", "link_url": "https://cseweb.ucsd.edu/~jmcauley/datasets.html#multi_aspect"}, {"dataset": "<PERSON><PERSON><PERSON>", "dataset_link": "", "user_num": "63,497", "item_num": "178,788", "inter_num": "1,000,000", "sparsity": "99.9912%", "type": "<PERSON>s", "link_name": "link", "link_url": "https://cseweb.ucsd.edu/~jmcauley/datasets.html#behance"}, {"dataset": "<PERSON><PERSON><PERSON><PERSON>", "dataset_link": "", "user_num": "542,706", "item_num": "243,247", "inter_num": "4,422,473", "sparsity": "99.9967%", "type": "Rating [0, 5]", "link_name": "link", "link_url": "http://yongfeng.me/dataset/"}, {"dataset": "EndoMondo", "dataset_link": "", "user_num": "1,104", "item_num": "253,020", "inter_num": "253,020", "sparsity": "99.9094%", "type": "Workout Logs", "link_name": "link", "link_url": "https://cseweb.ucsd.edu/~jmcauley/datasets.html#endomondo"}, {"dataset": "Food", "dataset_link": "", "user_num": "226,570", "item_num": "231,637", "inter_num": "1,132,367", "sparsity": "99.9978%", "type": "Rating [0, 5]", "link_name": "link", "link_url": "https://cseweb.ucsd.edu/~jmcauley/datasets.html#foodcom"}, {"dataset": "GoodReads", "dataset_link": "", "user_num": "876,145", "item_num": "2,360,650", "inter_num": "228,648,342", "sparsity": "99.9889%", "type": "Rating [0, 5]", "link_name": "link", "link_url": "https://cseweb.ucsd.edu/~jmcauley/datasets.html#goodreads"}, {"dataset": "KGRec", "dataset_link": "https://github.com/RUCAIBox/RecommenderSystems-Datasets/tree/master/dataset_info/KGRec", "user_num": "-", "item_num": "-", "inter_num": "-", "sparsity": "-", "type": "Click", "link_name": "link", "link_url": "https://www.upf.edu/web/mtg/kgrec"}, {"dataset": "ModCloth", "dataset_link": "", "user_num": "47,958", "item_num": "1,378", "inter_num": "82,790", "sparsity": "99.8747%", "type": "Rating [0, 5]", "link_name": "link", "link_url": "https://cseweb.ucsd.edu/~jmcauley/datasets.html#clothing_fit"}, {"dataset": "RateBeer", "dataset_link": "", "user_num": "29,265", "item_num": "110,369", "inter_num": "2,924,163", "sparsity": "99.9095%", "type": "Overall Rating [0, 20]", "link_name": "link", "link_url": "https://cseweb.ucsd.edu/~jmcauley/datasets.html#multi_aspect"}, {"dataset": "RentTheRunway", "dataset_link": "", "user_num": "105,571", "item_num": "5,850", "inter_num": "192,544", "sparsity": "99.9688%", "type": "Rating [0, 10]", "link_name": "link", "link_url": "https://cseweb.ucsd.edu/~jmcauley/datasets.html#clothing_fit"}, {"dataset": "Twitch", "dataset_link": "https://github.com/RUCAIBox/RecommenderSystems-Datasets/tree/master/dataset_info/Twitch", "user_num": "15,524,309", "item_num": "6,161,666", "inter_num": "474,676,929", "sparsity": "99.9995%", "type": "Click", "link_name": "link", "link_url": "https://cseweb.ucsd.edu/~jmcauley/datasets.html#twitch"}, {"dataset": "Criteo", "dataset_link": "", "user_num": "-", "item_num": "-", "inter_num": "45,850,617", "sparsity": "-", "type": "Click", "link_name": "script", "link_url": "https://github.com/RUCAIBox/RecDatasets/blob/master/conversion_tools/usage/Criteo.md"}, {"dataset": "<PERSON><PERSON>", "dataset_link": "", "user_num": "-", "item_num": "-", "inter_num": "40,428,967", "sparsity": "-", "type": "Click", "link_name": "script", "link_url": "https://github.com/RUCAIBox/RecDatasets/blob/master/conversion_tools/usage/Avazu.md"}, {"dataset": "iPinYou", "dataset_link": "https://github.com/RUCAIBox/RecommenderSystems-Datasets/tree/master/dataset_info/iPinYou", "user_num": "19,731,660", "item_num": "163", "inter_num": "24,637,657", "sparsity": "99.23%", "type": "View/Click", "link_name": "script", "link_url": "https://github.com/RUCAIBox/RecDatasets/blob/master/conversion_tools/usage/iPinYou.md"}, {"dataset": "Phishing websites", "dataset_link": "", "user_num": "-", "item_num": "-", "inter_num": "11,055", "sparsity": "-", "type": "Click", "link_name": "script", "link_url": "https://github.com/RUCAIBox/RecDatasets/blob/master/conversion_tools/usage/Phishing%20Websites.md"}, {"dataset": "Adult", "dataset_link": "", "user_num": "-", "item_num": "-", "inter_num": "32,561", "sparsity": "-", "type": "income>=50k [0, 1]", "link_name": "script", "link_url": "https://github.com/RUCAIBox/RecDatasets/blob/master/conversion_tools/usage/Adult.md"}, {"dataset": "Alibaba-iFashion", "dataset_link": "", "user_num": "3,569,112", "item_num": "4,463,302", "inter_num": "191,394,393", "sparsity": "99.9988%", "type": "Click", "link_name": "link", "link_url": "https://github.com/wenyuer/POG"}, {"dataset": "AliEC", "dataset_link": "", "user_num": "491,647", "item_num": "240,130", "inter_num": "1,366,056", "sparsity": "99.9988%", "type": "Click", "link_name": "link", "link_url": "https://tianchi.aliyun.com/dataset/dataDetail?dataId=56#1"}, {"dataset": "Music4All-Onion", "dataset_link": "", "user_num": "119,140", "item_num": "109,269", "inter_num": "252,984,396", "sparsity": "-", "type": "Click", "link_name": "script", "link_url": "https://github.com/RUCAIBox/RecSysDatasets/blob/master/conversion_tools/usage/Onion.md"}, {"dataset": "Amazon_M2", "dataset_link": "", "user_num": "3,606,349", "item_num": "1,410,675", "inter_num": "15,306,183", "sparsity": "-", "type": "Click", "link_name": "Link", "link_url": "https://www.aicrowd.com/challenges/amazon-kdd-cup-23-multilingual-recommendation-challenge"}]}