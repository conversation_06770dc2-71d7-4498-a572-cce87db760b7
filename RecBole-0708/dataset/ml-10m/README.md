INTERACTIONs DATASET FILE DESCRIPTION
------------------------------------------------------------------------------------
------------------------------------------------------------------------------------
The file ml-10m.inter comprising the ratings of users over the movies.
Each record/line in the file has the following fields: user_id, item_id, rating, timestamp

user_id: the id of the users and its type is token. 
item_id: the id of the movies and its type is token.
rating: the rating of the users over the movies, and its type is float.
timestamp: the UNIX timestamp of the rating, and its type is float.

MOVIES INFORMATION DATASET FILE DESCRIPTION
------------------------------------------------------------------------------------
------------------------------------------------------------------------------------
The file ml-10m.item comprising the attributes of the movies.
Each record/line in the file has the following fields: item_id, movie_title, release_year, class
 
item_id: the id of the movies and its type is token.
movie_title: the title of the movies, and its type is token_seq.
release_year: the year when movies were released, and its type is float.
genre: the genres of the movies, and its type is token_seq.

