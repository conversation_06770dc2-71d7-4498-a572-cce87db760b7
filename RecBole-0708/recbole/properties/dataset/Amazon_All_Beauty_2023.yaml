# 原子文件格式
field_separator: "\t"           # (str) 原子文件中不同列的分隔符
seq_separator: " "              # (str) 序列特征内部的分隔符

# 基本信息
USER_ID_FIELD: user_id          # (str) 用户ID特征的字段名
ITEM_ID_FIELD: item_id          # (str) 物品ID特征的字段名
RATING_FIELD: rating            # (str) 评分特征的字段名
TIME_FIELD: timestamp           # (str) 时间戳特征的字段名
seq_len: ~                      # (dict) 序列特征的字段名：每个序列的最大长度
LABEL_FIELD: label              # (str) 点式数据加载器生成的标签的预期字段名
threshold: ~                    # (dict) 根据配对生成0/1标签
NEG_PREFIX: neg_                # (str) 对式数据加载器的负采样前缀
numerical_features: []          # (list) 需要嵌入的浮点特征字段

# 选择性加载
load_col:                       # (dict) 原子文件的后缀：(list) 要加载的字段名
    inter: [user_id, item_id, rating, timestamp]   # 加载用户ID、物品ID、评分和时间戳
unload_col: ~                   # (dict) 原子文件的后缀：(list) 不加载的字段名
unused_col: ~                   # (dict) 原子文件的后缀：(list) 加载但不使用的字段名
additional_feat_suffix: ~       # (list) 控制加载额外的原子文件

# 过滤
rm_dup_inter: "first"                # (str) 是否删除重复的用户-物品交互
val_interval: ~                 # (dict) 通过{值字段(str): 区间(str)}中的值过滤交互
filter_inter_by_user_or_item: True    # (bool) 是否按用户或物品过滤交互
user_inter_num_interval: "[3,inf)"    # (str) 用于过滤交互的用户区间，如[A,B] / [A,B) / (A,B) / (A,B)
item_inter_num_interval: "[3,inf)"  # (str) 用于过滤交互的物品区间，如[A,B] / [A,B) / (A,B) / (A,B)

# 预处理
alias_of_user_id: ~             # (list) 与USER_ID_FIELD重新映射到相同索引系统的字段名
alias_of_item_id: ~             # (list) 与ITEM_ID_FIELD重新映射到相同索引系统的字段名
alias_of_entity_id: ~           # (list) 与ENTITY_ID_FIELD重新映射到相同索引系统的字段名
alias_of_relation_id: ~         # (list) 与RELATION_ID_FIELD重新映射到相同索引系统的字段名
preload_weight: ~               # (dict) 预加载权重，格式为{ID(令牌): 预训练向量(浮点类型)}
normalize_field: ~              # (list) 需要标准化的字段名列表
normalize_all: True             # (bool) 是否标准化所有浮点类型的字段
discretization: ~               # (dict) 离散化设置

# 序列模型需要
ITEM_LIST_LENGTH_FIELD: item_length   # (str) 表示物品序列长度的特征的字段名
LIST_SUFFIX: _list              # (str) 生成为序列的字段名的后缀
MAX_ITEM_LIST_LENGTH: 50        # (int) 每个生成序列的最大长度
POSITION_FIELD: position_id     # (str) 生成的位置序列的字段名

# 基于知识的模型需要
HEAD_ENTITY_ID_FIELD: head_id   # (str) 头实体ID特征的字段名
TAIL_ENTITY_ID_FIELD: tail_id   # (str) 尾实体ID特征的字段名
RELATION_ID_FIELD: relation_id  # (str) 关系ID特征的字段名
ENTITY_ID_FIELD: entity_id      # (str) 实体ID的字段名
kg_reverse_r: False             # (bool) 是否为双向边反转三元组的关系
entity_kg_num_interval: "[0,inf)"       # (str) 用于过滤知识图谱的实体区间
relation_kg_num_interval: "[0,inf)"     # (str) 用于过滤知识图谱的关系区间

# 基准测试 .inter 文件
benchmark_filename: ~           # (list) 预分割的用户-物品交互后缀列表 