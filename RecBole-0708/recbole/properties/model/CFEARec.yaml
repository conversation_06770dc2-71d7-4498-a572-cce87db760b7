# CFEARec Model Configuration
# Enhanced FEARec with User Spectrum Analysis and Adaptive Filtering at Input Layer

# Basic model parameters (consistent with FEARec)
embedding_size: 64               # (int) The embedding size of items
hidden_size: 64                  # (int) The hidden size of transformer
n_layers: 2                      # (int) The number of transformer layers
n_heads: 2                       # (int) The number of attention heads
inner_size: 256                  # (int) The inner hidden size in feed-forward layer
hidden_dropout_prob: 0.5         # (float) The dropout probability for hidden layer
attn_dropout_prob: 0.5           # (float) The dropout probability for attention layer
hidden_act: 'gelu'               # (str) The activation function in feed-forward layer
layer_norm_eps: 1e-12            # (float) The epsilon value in layer normalization
initializer_range: 0.02          # (float) The standard deviation for normal initialization

# Loss function parameters
loss_type: 'CE'                  # (str) The type of loss function ['BPR', 'CE']
lmd: 0.1                         # (float) The weight of unsupervised normalized CE loss
lmd_sem: 0.1                     # (float) The weight of supervised normalized CE loss

# Frequency domain parameters (from original FEARec)
global_ratio: 1.0                # (float) The ratio of frequency components
dual_domain: True                # (bool) Frequency domain processing or not
std: False                       # (bool) Use the specific time index or not
spatial_ratio: 0.0               # (float) The ratio of the spatial domain and frequency domain
fredom: False                    # (bool) Regularization in the frequency domain or not
fredom_type: None                # (str) The type of loss in different scenarios
topk_factor: 1                   # (int) To aggregate time delayed sequences with high autocorrelation
use_filter: True                 # (bool) Whether to use the original adaptive filter

# === 核心创新：用户频谱分析参数 ===
use_user_spectrum_analysis: True # (bool) Whether to enable user spectrum analysis at input layer
spectrum_analysis_weight: 0.05   # (float) Weight for spectrum analysis regularization loss
adaptive_filter_strength: 1.0   # (float) Strength of adaptive filtering (0.0-2.0)

# # === 对比学习参数 ===
# use_contrastive_learning: False  # (bool) Whether to enable contrastive learning between original and filtered sequences
# contrastive_weight: 0.1          # (float) Weight for contrastive learning loss
# contrastive_temperature: 0.08    # (float) Temperature parameter for InfoNCE loss

# 用户类型分类参数
user_type_entropy_weight: 0.1    # (float) Weight for user type entropy regularization
user_type_balance_weight: 0.01   # (float) Weight for user type balance regularization

# 滤波器参数
lpf_strength: 1.0                # (float) Low-pass filter strength for LPF users
hpf_strength: 1.0                # (float) High-pass filter strength for HPF users
bpf_strength: 1.0                # (float) Band-pass filter strength for BPF users
bsf_strength: 1.0                # (float) Band-stop filter strength for BSF users
apf_strength: 1.0                # (float) All-pass filter strength for APF users

# Training parameters
train_batch_size: 256            # (int) Training batch size
learning_rate: 0.001             # (float) Learning rate
epochs: 300                      # (int) Number of training epochs
eval_step: 1                     # (int) Evaluation step
stopping_step: 10                # (int) Early stopping step
train_neg_sample_args: ~
