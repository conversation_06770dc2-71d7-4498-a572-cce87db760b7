# -*- coding: utf-8 -*-
# @Time    : 2024/07/20
# <AUTHOR> CFEARec Team
# @Email   : <EMAIL>

r"""
CFEARec (Collaborative Frequency Enhanced Attention Recommender)
################################################

Reference:
    Enhanced version of FEARec with user spectrum analysis and adaptive filtering at input layer.
    Based on: <PERSON><PERSON><PERSON> et al. "Frequency Enhanced Hybrid Attention Network for Sequential Recommendation."
    In SIGIR 2023.

Key Innovation:
    - User Spectrum Analyzer: Analyzes user behavior patterns in frequency domain
    - Adaptive Spectrum Filter: Applies personalized filtering based on user types
    - Input Layer Integration: Processes sequences before entering the encoder
"""

import torch
from torch import nn
import torch.nn.functional as F
import math

from recbole.model.abstract_recommender import SequentialRecommender
from recbole.model.loss import BPRLoss
from recbole.model.layers import TransformerEncoder


class UserSpectrumAnalyzer(nn.Module):
    """用户频谱分析器：分析用户行为的频域特征并分类用户类型
    
    在输入层对用户序列进行频谱分析，识别用户行为模式：
    - LPF用户：低频能量占比高，长期固定兴趣，偏好变化缓慢
    - HPF用户：高频能量占比大，偏好变化快，受流行物品影响
    - BPF用户：明显峰值，周期性行为模式
    - BSF用户：兴趣发展不连续，存在明显的兴趣跳跃
    - APF用户：频率分量均匀分布，探索与稳定并存
    """
    
    def __init__(self, hidden_size, max_seq_len):
        super().__init__()
        self.hidden_size = hidden_size
        self.max_seq_len = max_seq_len
        self.freq_bins = max_seq_len // 2 + 1
        
        # 频谱特征提取网络
        self.spectrum_feature_extractor = nn.Sequential(
            nn.Linear(self.freq_bins, hidden_size),
            nn.LayerNorm(hidden_size),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(hidden_size, hidden_size // 2),
            nn.LayerNorm(hidden_size // 2),
            nn.ReLU(),
            nn.Dropout(0.1)
        )
        
        # 用户类型分类器 (5种滤波器类型)
        self.user_type_classifier = nn.Linear(hidden_size // 2, 5)
        
    def analyze_frequency_characteristics(self, sequence_emb):
        """分析序列的频域特征"""
        # FFT变换到频域
        spectrum = torch.fft.rfft(sequence_emb, dim=1, norm='ortho')  # [B, freq_bins, H]
        
        # 计算功率谱
        power_spectrum = torch.abs(spectrum) ** 2  # [B, freq_bins, H]
        
        # 沿隐藏维度平均，得到每个频率的平均功率
        avg_power_spectrum = power_spectrum.mean(dim=-1)  # [B, freq_bins]
        
        # 计算总能量
        total_energy = torch.sum(avg_power_spectrum, dim=1, keepdim=True)  # [B, 1]
        
        # 归一化功率谱
        norm_power_spectrum = avg_power_spectrum / (total_energy + 1e-10)  # [B, freq_bins]
        
        return {'norm_power_spectrum': norm_power_spectrum}, spectrum
        
    def classify_user_type(self, frequency_features):
        """基于频域特征分类用户类型"""
        # 使用归一化功率谱作为主要特征
        feature_input = frequency_features['norm_power_spectrum']  # [B, freq_bins]
        
        # 特征提取
        extracted_features = self.spectrum_feature_extractor(feature_input)  # [B, hidden_size//2]
        
        # 分类预测
        user_type_logits = self.user_type_classifier(extracted_features)  # [B, 5]
        user_type_probs = F.softmax(user_type_logits, dim=1)
        
        return user_type_probs
        
    def forward(self, sequence_emb):
        """
        Args:
            sequence_emb: [batch_size, seq_len, hidden_size] 输入序列嵌入
            
        Returns:
            user_type_probs: [batch_size, 5] 用户类型概率分布
            spectrum_info: dict 频谱分析信息
        """
        frequency_features, spectrum = self.analyze_frequency_characteristics(sequence_emb)
        user_type_probs = self.classify_user_type(frequency_features)
        
        spectrum_info = {
            'spectrum': spectrum,
            'features': frequency_features
        }
        
        return user_type_probs, spectrum_info


class AdaptiveSpectrumFilter(nn.Module):
    """自适应频谱过滤器：根据用户类型动态调整频域处理
    
    针对不同用户类型应用相应的滤波策略：
    - LPF用户：保留低频成分，代表稳定的长期兴趣
    - HPF用户：增强高频成分，突出最近的兴趣变化
    - BPF用户：选择性保留特定频率带，对应周期性行为模式
    - BSF用户：增强低频和高频信号，抑制中频信号
    - APF用户：轻微扰动，增强内容多样性
    """
    
    def __init__(self, max_seq_len, hidden_size):
        super().__init__()
        self.max_seq_len = max_seq_len
        self.hidden_size = hidden_size
        self.freq_bins = max_seq_len // 2 + 1
        
        # 5种滤波器的参数
        self.lpf_params = nn.Parameter(torch.ones(self.freq_bins))
        self.hpf_params = nn.Parameter(torch.ones(self.freq_bins))
        self.bpf_params = nn.Parameter(torch.ones(self.freq_bins))
        self.bsf_params = nn.Parameter(torch.ones(self.freq_bins))
        self.apf_params = nn.Parameter(torch.ones(self.freq_bins))
        
        # 门控网络，用于控制滤波强度
        self.gate_network = nn.Sequential(
            nn.Linear(hidden_size, hidden_size // 4),
            nn.ReLU(),
            nn.Linear(hidden_size // 4, 1),
            nn.Sigmoid()
        )
        
        # 初始化滤波器参数
        self._initialize_filters()
        
    def _initialize_filters(self):
        """初始化各种滤波器的频率响应"""
        freq_indices = torch.arange(self.freq_bins, dtype=torch.float32)
        normalized_freq = freq_indices / (self.freq_bins - 1)  # 归一化到[0,1]
        
        # 低通滤波器：保留低频，衰减高频
        lpf_cutoff = 0.3
        lpf_response = torch.exp(-((normalized_freq - 0) / lpf_cutoff) ** 2)
        lpf_response[normalized_freq > lpf_cutoff] *= torch.exp(-(normalized_freq[normalized_freq > lpf_cutoff] - lpf_cutoff) * 5)
        self.lpf_params.data = lpf_response
        
        # 高通滤波器：保留高频，衰减低频
        hpf_cutoff = 0.7
        hpf_response = torch.exp(-((normalized_freq - 1) / (1 - hpf_cutoff)) ** 2)
        hpf_response[normalized_freq < hpf_cutoff] *= torch.exp(-(hpf_cutoff - normalized_freq[normalized_freq < hpf_cutoff]) * 5)
        self.hpf_params.data = hpf_response
        
        # 带通滤波器：保留中频段
        bpf_center = 0.5
        bpf_bandwidth = 0.3
        bpf_response = torch.exp(-((normalized_freq - bpf_center) / bpf_bandwidth) ** 2)
        self.bpf_params.data = bpf_response
        
        # 带阻滤波器：阻止中频段
        bsf_response = 1.0 - bpf_response
        self.bsf_params.data = bsf_response
        
        # 全通滤波器：轻微随机扰动
        apf_response = torch.ones(self.freq_bins) + torch.randn(self.freq_bins) * 0.05
        apf_response = torch.clamp(apf_response, 0.8, 1.2)  # 限制扰动范围
        self.apf_params.data = apf_response
        
    def create_adaptive_filter(self, user_type_probs):
        """根据用户类型概率创建自适应滤波器"""
        # 将所有滤波器参数堆叠
        all_filters = torch.stack([
            self.lpf_params,  # 0: LPF
            self.hpf_params,  # 1: HPF  
            self.bpf_params,  # 2: BPF
            self.bsf_params,  # 3: BSF
            self.apf_params   # 4: APF
        ], dim=0)  # [5, freq_bins]
        
        # 根据用户类型概率加权组合滤波器
        filter_response = torch.matmul(user_type_probs, all_filters)  # [batch_size, freq_bins]
        
        return filter_response
        
    def forward(self, sequence_emb, user_type_probs):
        """应用自适应频谱过滤"""
        batch_size, seq_len, hidden_size = sequence_emb.shape
        
        # 计算门控因子
        seq_repr = torch.mean(sequence_emb, dim=1)  # [batch_size, hidden_size]
        gate_factor = self.gate_network(seq_repr)  # [batch_size, 1]
        
        # 转换到频域
        spectrum = torch.fft.rfft(sequence_emb, dim=1, norm='ortho')  # [batch_size, freq_bins, hidden_size]
        
        # 创建自适应滤波器
        filter_response = self.create_adaptive_filter(user_type_probs)  # [batch_size, freq_bins]
        
        # 扩展滤波器维度以匹配频谱
        filter_response = filter_response.unsqueeze(-1)  # [batch_size, freq_bins, 1]
        filter_response = filter_response.expand(-1, -1, hidden_size)  # [batch_size, freq_bins, hidden_size]
        
        # 应用滤波器
        filtered_spectrum = spectrum * filter_response
        
        # 转换回时域
        filtered_sequence = torch.fft.irfft(filtered_spectrum, n=seq_len, dim=1, norm='ortho')
        
        # 应用门控机制，控制滤波强度
        gate_factor = gate_factor.unsqueeze(1)  # [batch_size, 1, 1]
        output = gate_factor * filtered_sequence + (1 - gate_factor) * sequence_emb
        
        return output


class CFEARec(SequentialRecommender):
    """Collaborative Frequency Enhanced Attention Recommender

    基于FEARec的增强版本，在输入层集成用户频谱分析和自适应滤波
    """

    def __init__(self, config, dataset):
        super(CFEARec, self).__init__(config, dataset)

        # 加载参数
        self.dataset = dataset
        self.config = config
        self.n_layers = config["n_layers"]
        self.n_heads = config["n_heads"]
        self.hidden_size = config["hidden_size"]
        self.inner_size = config["inner_size"]
        self.hidden_dropout_prob = config["hidden_dropout_prob"]
        self.attn_dropout_prob = config["attn_dropout_prob"]
        self.hidden_act = config["hidden_act"]
        self.layer_norm_eps = config["layer_norm_eps"]

        self.lmd = config["lmd"]
        self.lmd_sem = config["lmd_sem"]

        self.initializer_range = config["initializer_range"]
        self.loss_type = config["loss_type"]

        # 用户频谱分析相关参数
        self.use_user_spectrum_analysis = config["use_user_spectrum_analysis"] if "use_user_spectrum_analysis" in config else True
        self.spectrum_analysis_weight = config["spectrum_analysis_weight"] if "spectrum_analysis_weight" in config else 0.1
        self.adaptive_filter_strength = config["adaptive_filter_strength"] if "adaptive_filter_strength" in config else 1.0

        # 对比学习相关参数
        self.use_contrastive_learning = config["use_contrastive_learning"] if "use_contrastive_learning" in config else False
        self.contrastive_weight = config["contrastive_weight"] if "contrastive_weight" in config else 0.1
        self.contrastive_temperature = config["contrastive_temperature"] if "contrastive_temperature" in config else 0.07

        # 定义层和损失函数
        self.item_embedding = nn.Embedding(
            self.n_items, self.hidden_size, padding_idx=0
        )
        self.position_embedding = nn.Embedding(self.max_seq_length, self.hidden_size)

        # === 核心创新：用户频谱分析器和自适应滤波器 ===
        if self.use_user_spectrum_analysis:
            self.user_spectrum_analyzer = UserSpectrumAnalyzer(
                self.hidden_size, self.max_seq_length
            )
            self.adaptive_spectrum_filter = AdaptiveSpectrumFilter(
                self.max_seq_length, self.hidden_size
            )

        # 使用标准的Transformer编码器
        self.item_encoder = TransformerEncoder(
            n_layers=self.n_layers,
            n_heads=self.n_heads,
            hidden_size=self.hidden_size,
            inner_size=self.inner_size,
            hidden_dropout_prob=self.hidden_dropout_prob,
            attn_dropout_prob=self.attn_dropout_prob,
            hidden_act=self.hidden_act,
            layer_norm_eps=self.layer_norm_eps,
        )

        self.LayerNorm = nn.LayerNorm(self.hidden_size, eps=self.layer_norm_eps)
        self.dropout = nn.Dropout(self.hidden_dropout_prob)

        if self.loss_type == "BPR":
            self.loss_fct = BPRLoss()
        elif self.loss_type == "CE":
            self.loss_fct = nn.CrossEntropyLoss()
        else:
            raise NotImplementedError("Make sure 'loss_type' in ['BPR', 'CE']!")

        # 对比学习相关组件
        if self.use_contrastive_learning:
            self.contrastive_projection = nn.Sequential(
                nn.Linear(self.hidden_size, self.hidden_size // 2),
                nn.ReLU(),
                nn.Linear(self.hidden_size // 2, self.hidden_size // 4)
            )

        # 参数初始化
        self.apply(self._init_weights)

    def _init_weights(self, module):
        """初始化权重"""
        if isinstance(module, (nn.Linear, nn.Embedding)):
            module.weight.data.normal_(mean=0.0, std=self.initializer_range)
        elif isinstance(module, nn.LayerNorm):
            module.bias.data.zero_()
            module.weight.data.fill_(1.0)
        if isinstance(module, nn.Linear) and module.bias is not None:
            module.bias.data.zero_()

    def get_attention_mask(self, item_seq):
        """生成注意力掩码"""
        attention_mask = (item_seq > 0).long()
        extended_attention_mask = attention_mask.unsqueeze(1).unsqueeze(2)

        max_len = attention_mask.size(-1)
        attn_shape = (1, max_len, max_len)
        subsequent_mask = torch.triu(torch.ones(attn_shape), diagonal=1)
        subsequent_mask = (subsequent_mask == 0).unsqueeze(1)
        subsequent_mask = subsequent_mask.long().to(item_seq.device)

        extended_attention_mask = extended_attention_mask * subsequent_mask
        extended_attention_mask = extended_attention_mask.to(dtype=next(self.parameters()).dtype)
        extended_attention_mask = (1.0 - extended_attention_mask) * -10000.0

        return extended_attention_mask

    def forward(self, item_seq, item_seq_len):
        """前向传播 - 在位置1集成用户频谱分析和自适应滤波"""
        position_ids = torch.arange(
            item_seq.size(1), dtype=torch.long, device=item_seq.device
        )
        position_ids = position_ids.unsqueeze(0).expand_as(item_seq)
        position_embedding = self.position_embedding(position_ids)

        item_emb = self.item_embedding(item_seq)
        input_emb = item_emb + position_embedding

        # === 位置1：输入层频谱预处理 ===
        if self.use_user_spectrum_analysis:
            # 保存原始输入用于对比学习
            original_input_emb = input_emb.clone() if self.use_contrastive_learning else None

            # 1. 用户频谱分析
            user_type_probs, spectrum_info = self.user_spectrum_analyzer(input_emb)

            # 2. 自适应频谱过滤
            filtered_input_emb = self.adaptive_spectrum_filter(input_emb, user_type_probs)

            # 存储频谱信息用于损失计算
            self.current_spectrum_info = {
                'user_type_probs': user_type_probs,
                'spectrum_info': spectrum_info,
                'original_input': original_input_emb,
                'filtered_input': filtered_input_emb
            }

            input_emb = filtered_input_emb

        input_emb = self.LayerNorm(input_emb)
        input_emb = self.dropout(input_emb)

        extended_attention_mask = self.get_attention_mask(item_seq)

        trm_output = self.item_encoder(
            input_emb, extended_attention_mask, output_all_encoded_layers=True
        )
        output = trm_output[-1]
        output = self.gather_indexes(output, item_seq_len - 1)

        return output

    def calculate_loss(self, interaction):
        """计算损失函数"""
        item_seq = interaction[self.ITEM_SEQ]
        item_seq_len = interaction[self.ITEM_SEQ_LEN]
        seq_output = self.forward(item_seq, item_seq_len)
        pos_items = interaction[self.POS_ITEM_ID]

        # 主要推荐损失
        if self.loss_type == "BPR":
            neg_items = interaction[self.NEG_ITEM_ID]
            pos_items_emb = self.item_embedding(pos_items)
            neg_items_emb = self.item_embedding(neg_items)
            pos_score = torch.sum(seq_output * pos_items_emb, dim=-1)
            neg_score = torch.sum(seq_output * neg_items_emb, dim=-1)
            loss = self.loss_fct(pos_score, neg_score)
        else:  # CE
            test_item_emb = self.item_embedding.weight
            logits = torch.matmul(seq_output, test_item_emb.transpose(0, 1))
            loss = self.loss_fct(logits, pos_items)

        # 频谱分析正则化损失
        if self.use_user_spectrum_analysis and hasattr(self, 'current_spectrum_info'):
            spectrum_loss = self._calculate_spectrum_regularization_loss()
            loss += self.spectrum_analysis_weight * spectrum_loss

        # 对比学习损失
        if self.use_contrastive_learning and hasattr(self, 'current_spectrum_info'):
            contrastive_loss = self._calculate_contrastive_loss(item_seq_len)
            loss += self.contrastive_weight * contrastive_loss

        return loss

    def _calculate_spectrum_regularization_loss(self):
        """计算频谱分析的正则化损失"""
        if not hasattr(self, 'current_spectrum_info'):
            return torch.tensor(0.0, device=next(self.parameters()).device)

        user_type_probs = self.current_spectrum_info['user_type_probs']

        # 用户类型分布的熵正则化，鼓励明确的用户类型分类
        entropy_loss = -torch.mean(torch.sum(user_type_probs * torch.log(user_type_probs + 1e-10), dim=1))

        # 用户类型平衡正则化，避免所有用户被分类为同一类型
        avg_type_probs = torch.mean(user_type_probs, dim=0)
        balance_loss = -torch.sum(avg_type_probs * torch.log(avg_type_probs + 1e-10))

        return entropy_loss + 0.1 * balance_loss

    def _calculate_contrastive_loss(self, item_seq_len):
        """计算原始序列和滤波序列之间的对比学习损失"""
        if not hasattr(self, 'current_spectrum_info') or \
           'original_input' not in self.current_spectrum_info or \
           'filtered_input' not in self.current_spectrum_info:
            return torch.tensor(0.0, device=next(self.parameters()).device)

        original_input = self.current_spectrum_info['original_input']
        filtered_input = self.current_spectrum_info['filtered_input']

        if original_input is None or filtered_input is None:
            return torch.tensor(0.0, device=next(self.parameters()).device)

        # 对原始和滤波序列进行编码
        original_emb = self.LayerNorm(original_input)
        original_emb = self.dropout(original_emb)

        filtered_emb = self.LayerNorm(filtered_input)
        filtered_emb = self.dropout(filtered_emb)

        # 获取序列表示（使用最后一个有效位置）
        batch_size = original_emb.size(0)
        original_seq_output = self.gather_indexes(original_emb, item_seq_len - 1)  # [B, H]
        filtered_seq_output = self.gather_indexes(filtered_emb, item_seq_len - 1)  # [B, H]

        # 投影到对比学习空间
        original_proj = self.contrastive_projection(original_seq_output)  # [B, H//4]
        filtered_proj = self.contrastive_projection(filtered_seq_output)  # [B, H//4]

        # 归一化
        original_proj = F.normalize(original_proj, dim=1)
        filtered_proj = F.normalize(filtered_proj, dim=1)

        # 计算InfoNCE损失
        # 正样本：原始序列和对应的滤波序列
        # 负样本：原始序列和其他样本的滤波序列

        # 计算相似度矩阵
        sim_matrix = torch.matmul(original_proj, filtered_proj.T) / self.contrastive_temperature  # [B, B]

        # 对角线元素是正样本对
        positive_mask = torch.eye(batch_size, device=sim_matrix.device, dtype=torch.bool)

        # 计算InfoNCE损失
        # 对于每个原始序列，其对应的滤波序列是正样本，其他滤波序列是负样本
        labels = torch.arange(batch_size, device=sim_matrix.device)
        contrastive_loss = F.cross_entropy(sim_matrix, labels)

        return contrastive_loss

    def predict(self, interaction):
        """预测单个物品的得分"""
        item_seq = interaction[self.ITEM_SEQ]
        item_seq_len = interaction[self.ITEM_SEQ_LEN]
        test_item = interaction[self.ITEM_ID]
        seq_output = self.forward(item_seq, item_seq_len)
        test_item_emb = self.item_embedding(test_item)
        scores = torch.mul(seq_output, test_item_emb).sum(dim=1)
        return scores

    def full_sort_predict(self, interaction):
        """预测所有物品的得分"""
        item_seq = interaction[self.ITEM_SEQ]
        item_seq_len = interaction[self.ITEM_SEQ_LEN]
        seq_output = self.forward(item_seq, item_seq_len)
        test_items_emb = self.item_embedding.weight
        scores = torch.matmul(seq_output, test_items_emb.transpose(0, 1))
        return scores

    def get_user_spectrum_analysis(self, interaction):
        """获取用户频谱分析结果（用于分析和可视化）"""
        item_seq = interaction[self.ITEM_SEQ]
        item_seq_len = interaction[self.ITEM_SEQ_LEN]

        # 只进行前向传播到频谱分析部分
        position_ids = torch.arange(
            item_seq.size(1), dtype=torch.long, device=item_seq.device
        )
        position_ids = position_ids.unsqueeze(0).expand_as(item_seq)
        position_embedding = self.position_embedding(position_ids)

        item_emb = self.item_embedding(item_seq)
        input_emb = item_emb + position_embedding

        if self.use_user_spectrum_analysis:
            user_type_probs, spectrum_info = self.user_spectrum_analyzer(input_emb)

            # 解析用户类型
            user_types = torch.argmax(user_type_probs, dim=1)
            type_names = ['LPF', 'HPF', 'BPF', 'BSF', 'APF']

            return {
                'user_type_probs': user_type_probs,
                'user_types': user_types,
                'type_names': [type_names[t.item()] for t in user_types],
                'spectrum_info': spectrum_info
            }
        else:
            return None
