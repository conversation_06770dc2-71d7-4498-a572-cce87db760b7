---
name: Bug 报告
about: 提交一份 bug 报告，帮助 RecBole 变得更好
title: "[\U0001F41BBUG] 用一句话描述您的问题。"
labels: bug
assignees: ''

---

**描述这个 bug**
对 bug 作一个清晰简明的描述。

**如何复现**
复现这个 bug 的步骤：
1. 您引入的额外 yaml 文件
2. 您的代码
3. 您的运行脚本

**预期**
对您的预期作清晰简明的描述。

**屏幕截图**
添加屏幕截图以帮助解释您的问题。（可选）

**链接**
添加能够复现 bug 的代码链接，如 Colab 或者其他在线 Jupyter 平台。（可选）

**实验环境（请补全下列信息）：**
 - 操作系统: [如 Linux, macOS 或 Windows]
- RecBole 版本 [如 0.1.0]
 - Python 版本 [如 3.79]
- PyTorch 版本 [如 1.60]
- cudatoolkit 版本 [如 9.2, none]
