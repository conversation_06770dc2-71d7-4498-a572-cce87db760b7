name: <PERSON><PERSON><PERSON><PERSON> tests

# Controls when the action will run. 
on:
  # Triggers the workflow on push or pull request events but only for the master branch
  push:
  pull_request:

  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch:

jobs:
  build:

    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: [3.8]
    defaults:
      run:
        shell: bash -l {0}

    steps:
    - uses: actions/checkout@v2
    - name: Setup Miniconda
      uses: conda-incubator/setup-miniconda@v2
      with:
        python-version: ${{ matrix.python-version }}
        channels: conda-forge
        channel-priority: true
        auto-activate-base: true
    # install setuptools as a interim solution for bugs in PyTorch 1.10.2 (#69904)
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install protobuf==3.19.0
        pip install hyperopt==0.2.5
        pip install pytest
        pip install dgl==0.9.1
        pip install xgboost
        pip install community
        pip install networkx
        pip install python-louvain
        pip install lightgbm
        if [ -f requirements.txt ]; then pip install -r requirements.txt; fi
        conda list
        conda install -c conda-forge faiss-cpu
        pip install torch-scatter -f https://data.pyg.org/whl/torch-`python -c "import torch;print(torch.__version__)"`.html
        pip install setuptools==59.5.0
        pip install plotly
        pip install kmeans-pytorch
    # Use "python -m pytest" instead of "pytest" to fix imports
    - name: Test Overall
      run: |
        python run_recbole.py --model=BPR --epochs=2
    - name: Test metrics
      run: |
        python -m pytest -v tests/metrics
    - name: Test data
      run: |
        python -m pytest -v tests/data
    - name: Test evaluation_setting
      run: |
        python -m pytest -v tests/evaluation_setting
    - name: Test model
      run: |
        python -m pytest -v tests/model/test_model_auto.py
    - name: Test config
      run: |
        python -m pytest -v tests/config/test_config.py
        export PYTHONPATH=.
        python tests/config/test_command_line.py --use_gpu=False --valid_metric=Recall@10 --split_ratio=[0.7,0.2,0.1] --metrics='["Recall"]' --topk=[10] --epochs=200 --eval_setting='LO_RS' --learning_rate=0.3
    - name: Test hyper_tuning
      run: |
        python -m pytest -v tests/hyper_tuning/test_hyper_tuning.py
  # Use black to test code format
  # Reference code:
  #     https://black.readthedocs.io/en/stable/integrations/github_actions.html
  lint:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Install dependencies
        run: |
          pip install black[jupyter]
      - name: Test code format
        uses: psf/black@stable
        id: action-black
        with:
          options: "."
      - name: Apply code-format changes
        uses: stefanzweifel/git-auto-commit-action@v4
        with:
          commit_message: Format Python code according to PEP8
