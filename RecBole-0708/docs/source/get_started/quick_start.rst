Quick Start
===============
Here is a quick-start example for using RecBole. RecBole supports general recommendation, sequential recommendation, context-aware recommendation and knowledge-based recommendation. We will select a representative model from each type of recommendation to show you how to train and test on the **ml-100k** dataset from both **API**
and **source code**.

.. toctree::
   :maxdepth: 1

   started/general
   started/sequential
   started/context-aware
   started/knowledge-based

In-depth Usage
-------------------
For a more in-depth usage about RecB<PERSON>, take a look at

- :doc:`../user_guide/config_settings`
- :doc:`../user_guide/data_intro`
- :doc:`../user_guide/model_intro`
- :doc:`../user_guide/train_eval_intro`
- :doc:`../user_guide/usage`
