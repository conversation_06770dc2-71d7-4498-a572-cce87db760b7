package:
  name: recbole
  version: 1.2.1

source:
  path: ../

requirements:
  build:
    - python
  host:
    - python
    - numpy >=1.17.2
    - scipy >=1.6.0
    - pandas >=1.3.0
    - tqdm >=4.48.2
    - pyyaml >=5.1.0
    - scikit-learn >=0.23.2
    - pytorch >=1.10.0
    - colorlog >=4.7.2
    - colorama >=0.4.4
    - tensorboard >=2.5.0
    - tabulate >=0.8.10
    - plotly >=4.0.0
    - texttable >=0.9.0
    - psutil >=5.9.0
    - ray-tune >=1.6.0
  run:
    - python
    - numpy >=1.17.2
    - scipy >=1.6.0
    - pandas >=1.3.0
    - tqdm >=4.48.2
    - pyyaml >=5.1.0
    - scikit-learn >=0.23.2
    - pytorch >=1.10.0
    - colorlog >=4.7.2
    - colorama >=0.4.4
    - tensorboard >=2.5.0
    - tabulate >=0.8.10
    - plotly >=4.0.0
    - texttable >=0.9.0
    - psutil >=5.9.0
    - ray-tune >=1.6.0
test:
  imports:
    - recbole

about:
  home: https://recbole.io/
  license: MIT
  summary: "A unified, comprehensive and efficient recommendation library"
  
