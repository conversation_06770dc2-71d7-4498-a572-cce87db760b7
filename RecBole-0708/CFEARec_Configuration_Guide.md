# CFEARec 配置指南

## 🎯 概述

CFEARec模型现在支持两种方式来启用对比学习：
1. **配置文件方式**：修改`CFEARec.yaml`配置文件（推荐）
2. **命令行参数方式**：使用命令行参数覆盖配置文件

## 📋 配置文件方式（推荐）

### 1. 修改配置文件

编辑 `recbole/properties/model/CFEARec.yaml` 文件：

```yaml
# === 对比学习参数 ===
use_contrastive_learning: True   # 设置为True启用对比学习
contrastive_weight: 0.1          # 对比学习损失权重
contrastive_temperature: 0.07    # InfoNCE损失温度参数
```

### 2. 直接运行

修改配置文件后，直接运行即可启用对比学习：

```bash
# 使用配置文件中的对比学习设置
python run_recbole.py --model CFEARec --dataset Amazon_All_Beauty
```

### 3. 验证配置

运行时会显示：
```
📋 Using CFEARec configuration from config file
```

并且在日志中会记录完整的配置文件内容，包括：
```
📄 CFEARec Model Configuration File
================================================================================
📍 Config file path: recbole/properties/model/CFEARec.yaml
📋 Configuration content:
--------------------------------------------------------------------------------
  1: # CFEARec Model Configuration
  2: # Enhanced FEARec with User Spectrum Analysis and Adaptive Filtering at Input Layer
  ...
 37: use_contrastive_learning: True  # (bool) Whether to enable contrastive learning
 38: contrastive_weight: 0.1          # (float) Weight for contrastive learning loss
 39: contrastive_temperature: 0.07    # (float) Temperature parameter for InfoNCE loss
  ...
```

## 🚀 命令行参数方式

### 1. 启用对比学习

```bash
# 使用命令行参数启用对比学习（会覆盖配置文件）
python run_recbole.py --model CFEARec --dataset Amazon_All_Beauty --enable_contrastive
```

### 2. 自定义参数

```bash
# 自定义对比学习参数
python run_recbole.py --model CFEARec --dataset Amazon_All_Beauty \
    --enable_contrastive \
    --contrastive_weight 0.15 \
    --contrastive_temperature 0.05
```

### 3. 验证配置

运行时会显示：
```
🔥 CFEARec Contrastive Learning ENABLED (via command line)
   - Contrastive Weight: 0.1
   - Temperature: 0.07
```

## 📊 配置参数详解

### 核心参数

| 参数名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `use_contrastive_learning` | bool | False | 是否启用对比学习 |
| `contrastive_weight` | float | 0.1 | 对比学习损失权重 |
| `contrastive_temperature` | float | 0.07 | InfoNCE损失温度参数 |

### 频谱分析参数

| 参数名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `use_user_spectrum_analysis` | bool | True | 是否启用用户频谱分析 |
| `spectrum_analysis_weight` | float | 0.05 | 频谱分析正则化权重 |
| `adaptive_filter_strength` | float | 1.0 | 自适应滤波强度 |

## 🔧 参数调优建议

### 1. contrastive_weight（对比学习权重）

- **0.05-0.08**: 适合小数据集或噪声较多的数据
- **0.1-0.15**: 适合大多数中等规模数据集（推荐）
- **0.2-0.3**: 适合大数据集或需要强对比学习的场景

### 2. contrastive_temperature（温度参数）

- **0.05-0.07**: 严格的对比学习，适合高质量数据（推荐）
- **0.08-0.1**: 中等严格程度，适合一般数据集
- **0.1-0.2**: 宽松的对比学习，适合噪声数据

## 📈 使用场景

### 场景1：快速测试（使用配置文件）

```bash
# 1. 修改CFEARec.yaml中的use_contrastive_learning为True
# 2. 直接运行
python run_recbole.py --model CFEARec --dataset Amazon_All_Beauty
```

### 场景2：参数调优（使用命令行）

```bash
# 测试不同的对比学习权重
for weight in 0.05 0.1 0.15 0.2; do
    python run_recbole.py --model CFEARec --dataset Amazon_All_Beauty \
        --enable_contrastive --contrastive_weight $weight
done
```

### 场景3：批量实验（混合方式）

```bash
# 1. 在配置文件中设置基础参数
# 2. 使用命令行覆盖特定参数
python run_recbole.py --model CFEARec --dataset Amazon_All_Beauty \
    --enable_contrastive --contrastive_temperature 0.05
```

## 🔍 日志记录功能

每次运行CFEARec模型时，系统会自动记录：

1. **配置文件内容**：完整的CFEARec.yaml文件内容
2. **运行时参数**：实际使用的参数值
3. **组件状态**：对比学习组件是否正确创建

### 日志示例

```
================================================================================
📄 CFEARec Model Configuration File
================================================================================
📍 Config file path: recbole/properties/model/CFEARec.yaml
📋 Configuration content:
--------------------------------------------------------------------------------
  1: # CFEARec Model Configuration
  ...
 37: use_contrastive_learning: True
 38: contrastive_weight: 0.1
 39: contrastive_temperature: 0.07
  ...
--------------------------------------------------------------------------------
✅ CFEARec configuration logged successfully
================================================================================
🔧 Current Runtime Parameters:
   - use_user_spectrum_analysis: True
   - spectrum_analysis_weight: 0.05
   - use_contrastive_learning: True
   - contrastive_weight: 0.1
   - contrastive_temperature: 0.07
================================================================================
```

## ✅ 验证方法

### 1. 快速验证

```bash
# 运行配置测试脚本
python test_config_loading.py
```

预期输出：
```
🎉 SUCCESS: Contrastive learning is ENABLED from config file!
✅ Contrastive projection layer created
```

### 2. 完整验证

```bash
# 运行完整的对比学习测试
python test_cfearec_contrastive.py
```

预期输出：
```
✅ Contrastive loss: 4.1152
🎉 All tests passed! CFEARec with contrastive learning is working correctly.
```

## 🚨 注意事项

1. **优先级**：命令行参数 > 配置文件设置
2. **兼容性**：两种方式可以混合使用
3. **日志记录**：每次运行都会记录完整的配置信息
4. **参数验证**：系统会自动验证参数的有效性

## 📝 最佳实践

1. **开发阶段**：使用配置文件方式，便于快速迭代
2. **实验阶段**：使用命令行参数方式，便于参数调优
3. **生产阶段**：使用配置文件方式，确保配置的一致性和可追溯性

通过这种灵活的配置方式，您可以根据不同的需求选择最适合的使用方法！
