# CFEARec with Contrastive Learning Configuration
# This configuration enables contrastive learning between original and filtered sequences

# Basic model parameters
embedding_size: 64
hidden_size: 64
n_layers: 2
n_heads: 2
inner_size: 256
hidden_dropout_prob: 0.5
attn_dropout_prob: 0.5
hidden_act: 'gelu'
layer_norm_eps: 1e-12
initializer_range: 0.02

# Loss function parameters
loss_type: 'CE'
lmd: 0.1
lmd_sem: 0.1

# Frequency domain parameters
global_ratio: 1.0
dual_domain: True
std: False
spatial_ratio: 0.0
fredom: False
fredom_type: None
topk_factor: 1
use_filter: True

# User spectrum analysis parameters
use_user_spectrum_analysis: True
spectrum_analysis_weight: 0.05
adaptive_filter_strength: 1.0

# === Enable Contrastive Learning ===
use_contrastive_learning: True   # Enable contrastive learning
contrastive_weight: 0.1          # Weight for contrastive learning loss
contrastive_temperature: 0.07    # Temperature for InfoNCE loss

# User type classification parameters
user_type_entropy_weight: 0.1
user_type_balance_weight: 0.01

# Filter parameters
lpf_strength: 1.0
hpf_strength: 1.0
bpf_strength: 1.0
bsf_strength: 1.0
apf_strength: 1.0

# Training parameters
train_batch_size: 256
learning_rate: 0.001
epochs: 300
eval_step: 1
stopping_step: 10
train_neg_sample_args: ~

# Dataset and evaluation
metrics: ['Recall', 'NDCG']
topk: [5, 10, 20]
valid_metric: 'NDCG@10'
