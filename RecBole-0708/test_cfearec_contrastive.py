#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script for CFEARec with contrastive learning
"""

import torch
import torch.nn as nn
from recbole.config import Config
from recbole.data import create_dataset, data_preparation
from recbole.model.sequential_recommender.cfearec import CFEARec


def test_cfearec_contrastive():
    """Test CFEARec model with contrastive learning enabled"""
    
    print("🧪 Testing CFEARec with Contrastive Learning")
    print("=" * 50)
    
    # Create configuration
    config_dict = {
        'model': 'CFEARec',
        'dataset': 'ml-100k',
        'data_path': 'dataset/',
        
        # Basic model parameters
        'embedding_size': 64,
        'hidden_size': 64,
        'n_layers': 2,
        'n_heads': 2,
        'inner_size': 256,
        'hidden_dropout_prob': 0.5,
        'attn_dropout_prob': 0.5,
        'hidden_act': 'gelu',
        'layer_norm_eps': 1e-12,
        'initializer_range': 0.02,
        
        # Loss function
        'loss_type': 'CE',
        'lmd': 0.1,
        'lmd_sem': 0.1,
        
        # User spectrum analysis
        'use_user_spectrum_analysis': True,
        'spectrum_analysis_weight': 0.05,
        'adaptive_filter_strength': 1.0,
        
        # Contrastive learning parameters
        'use_contrastive_learning': True,
        'contrastive_weight': 0.1,
        'contrastive_temperature': 0.07,
        
        # Training parameters
        'train_batch_size': 64,
        'learning_rate': 0.001,
        'epochs': 2,  # Just for testing
        'eval_step': 1,
        'stopping_step': 10,
        
        # Evaluation
        'metrics': ['Recall', 'NDCG'],
        'topk': [5, 10],
        'valid_metric': 'NDCG@10'
    }
    
    try:
        # Create config
        config = Config(model='CFEARec', dataset='ml-100k', config_dict=config_dict)
        print(f"✅ Config created successfully")
        
        # Create dataset
        dataset = create_dataset(config)
        print(f"✅ Dataset created: {len(dataset)} interactions")
        
        # Data preparation
        train_data, valid_data, test_data = data_preparation(config, dataset)
        print(f"✅ Data prepared - Train: {len(train_data.dataset)}, Valid: {len(valid_data.dataset)}, Test: {len(test_data.dataset)}")
        
        # Create model
        model = CFEARec(config, dataset)
        print(f"✅ CFEARec model created")
        
        # Check contrastive learning components
        if hasattr(model, 'use_contrastive_learning') and model.use_contrastive_learning:
            print(f"✅ Contrastive learning ENABLED")
            print(f"   - Contrastive weight: {model.contrastive_weight}")
            print(f"   - Temperature: {model.contrastive_temperature}")
            
            if hasattr(model, 'contrastive_projection'):
                print(f"✅ Contrastive projection layer created")
            else:
                print(f"❌ Contrastive projection layer missing")
        else:
            print(f"❌ Contrastive learning NOT enabled")
        
        # Test forward pass
        print("\n🔄 Testing forward pass...")
        model.eval()
        
        # Get a batch of data
        for batch_data in train_data:
            item_seq = batch_data['item_id_list']
            item_seq_len = batch_data['item_length']
            
            print(f"   - Batch size: {item_seq.size(0)}")
            print(f"   - Sequence length: {item_seq.size(1)}")
            
            # Forward pass
            with torch.no_grad():
                seq_output = model.forward(item_seq, item_seq_len)
                print(f"✅ Forward pass successful - Output shape: {seq_output.shape}")
                
                # Check if spectrum info is stored
                if hasattr(model, 'current_spectrum_info'):
                    spectrum_info = model.current_spectrum_info
                    print(f"✅ Spectrum info stored:")
                    print(f"   - User type probs shape: {spectrum_info['user_type_probs'].shape}")
                    
                    if model.use_contrastive_learning:
                        if 'original_input' in spectrum_info and spectrum_info['original_input'] is not None:
                            print(f"✅ Original input saved for contrastive learning")
                        if 'filtered_input' in spectrum_info and spectrum_info['filtered_input'] is not None:
                            print(f"✅ Filtered input saved for contrastive learning")
                else:
                    print(f"❌ Spectrum info not stored")
            
            break  # Only test one batch
        
        # Test loss calculation
        print("\n🔄 Testing loss calculation...")
        model.train()
        
        for batch_data in train_data:
            try:
                loss = model.calculate_loss(batch_data)
                print(f"✅ Loss calculation successful - Loss: {loss.item():.4f}")
                
                # Test contrastive loss specifically
                if model.use_contrastive_learning and hasattr(model, 'current_spectrum_info'):
                    contrastive_loss = model._calculate_contrastive_loss(batch_data['item_length'])
                    print(f"✅ Contrastive loss: {contrastive_loss.item():.4f}")
                
                break
            except Exception as e:
                print(f"❌ Loss calculation failed: {e}")
                raise
        
        print("\n🎉 All tests passed! CFEARec with contrastive learning is working correctly.")
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = test_cfearec_contrastive()
    if success:
        print("\n✅ CFEARec contrastive learning test completed successfully!")
    else:
        print("\n❌ CFEARec contrastive learning test failed!")
