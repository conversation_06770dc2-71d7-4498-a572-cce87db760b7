# CFEARec 使用示例

## 🚀 快速开始

### 1. 基础使用 - 标准CFEARec
```bash
# 使用默认参数运行CFEARec
python run_recbole.py --model CFEARec --dataset Amazon_All_Beauty
```

### 2. 启用对比学习 - 推荐配置
```bash
# 启用对比学习，获得更好的推荐性能
python run_recbole.py --model CFEARec --dataset Amazon_All_Beauty --enable_contrastive
```

### 3. 自定义对比学习参数
```bash
# 调整对比学习参数以适应特定数据集
python run_recbole.py --model CFEARec --dataset Amazon_All_Beauty \
    --enable_contrastive \
    --contrastive_weight 0.15 \
    --contrastive_temperature 0.05
```

## 📊 不同数据集的推荐配置

### 小型数据集 (如 ml-100k)
```bash
# 小数据集建议使用较小的对比学习权重
python run_recbole.py --model CFEARec --dataset ml-100k \
    --enable_contrastive \
    --contrastive_weight 0.05 \
    --contrastive_temperature 0.1
```

### 中型数据集 (如 Amazon_All_Beauty)
```bash
# 中型数据集使用默认参数效果较好
python run_recbole.py --model CFEARec --dataset Amazon_All_Beauty \
    --enable_contrastive \
    --contrastive_weight 0.1 \
    --contrastive_temperature 0.07
```

### 大型数据集 (如 Amazon_Books)
```bash
# 大数据集可以使用更强的对比学习
python run_recbole.py --model CFEARec --dataset Amazon_Books \
    --enable_contrastive \
    --contrastive_weight 0.2 \
    --contrastive_temperature 0.05
```

## 🔧 高级配置

### 使用配置文件
```bash
# 使用预定义的对比学习配置文件
python run_recbole.py --model CFEARec --dataset Amazon_All_Beauty \
    --config_files cfearec_contrastive_config.yaml
```

### 使用专门的对比学习脚本
```bash
# 使用功能更丰富的对比学习脚本
python run_cfearec_contrastive.py --dataset Amazon_All_Beauty \
    --enable_contrastive \
    --batch_size 512 \
    --learning_rate 0.0005 \
    --epochs 100
```

## 🎯 性能优化建议

### 1. 参数调优策略

#### contrastive_weight (对比学习权重)
- **0.05-0.08**: 适合小数据集或噪声较多的数据
- **0.1-0.15**: 适合大多数中等规模数据集 (推荐)
- **0.2-0.3**: 适合大数据集或需要强对比学习的场景

#### contrastive_temperature (温度参数)
- **0.05-0.07**: 严格的对比学习，适合高质量数据 (推荐)
- **0.08-0.1**: 中等严格程度，适合一般数据集
- **0.1-0.2**: 宽松的对比学习，适合噪声数据

### 2. 训练策略

#### 快速验证
```bash
# 使用较少的epochs快速验证效果
python run_recbole.py --model CFEARec --dataset Amazon_All_Beauty \
    --enable_contrastive --epochs 50
```

#### 完整训练
```bash
# 完整训练以获得最佳性能
python run_recbole.py --model CFEARec --dataset Amazon_All_Beauty \
    --enable_contrastive --epochs 300 --stopping_step 15
```

## 📈 性能监控

### 1. 关键指标监控
在训练过程中，重点关注以下指标的变化：
- **NDCG@10**: 整体推荐质量
- **MRR@5**: 排序准确性
- **Recall@5**: 高精度召回

### 2. 对比学习效果验证
```bash
# 先运行标准版本作为基线
python run_recbole.py --model CFEARec --dataset Amazon_All_Beauty

# 再运行对比学习版本进行比较
python run_recbole.py --model CFEARec --dataset Amazon_All_Beauty --enable_contrastive
```

## 🛠️ 故障排除

### 常见问题及解决方案

#### 1. 内存不足
```bash
# 减小batch size
python run_recbole.py --model CFEARec --dataset Amazon_All_Beauty \
    --enable_contrastive --train_batch_size 128
```

#### 2. 训练不稳定
```bash
# 降低对比学习权重和学习率
python run_recbole.py --model CFEARec --dataset Amazon_All_Beauty \
    --enable_contrastive \
    --contrastive_weight 0.05 \
    --learning_rate 0.0005
```

#### 3. 性能下降
```bash
# 调整温度参数
python run_recbole.py --model CFEARec --dataset Amazon_All_Beauty \
    --enable_contrastive \
    --contrastive_temperature 0.1
```

## 🔬 实验建议

### A/B测试流程
1. **基线测试**: 运行标准CFEARec获得基线性能
2. **对比学习测试**: 启用对比学习并记录性能提升
3. **参数调优**: 基于初始结果调整参数
4. **最终验证**: 使用最佳参数进行完整训练

### 实验记录模板
```
实验日期: 2025-07-24
数据集: Amazon_All_Beauty
配置: CFEARec + 对比学习
参数: contrastive_weight=0.1, temperature=0.07

结果:
- NDCG@10: 0.3012 (vs 基线 0.2755, +9.3%)
- MRR@5: 0.2304 (vs 基线 0.1970, +17.0%)
- Recall@5: 0.4608 (vs 基线 0.4480, +2.9%)

结论: 对比学习显著提升了推荐质量，特别是排序准确性
```

## 📚 进阶使用

### 自定义配置文件
创建 `my_cfearec_config.yaml`:
```yaml
# 自定义CFEARec配置
use_contrastive_learning: True
contrastive_weight: 0.12
contrastive_temperature: 0.06

# 其他自定义参数
hidden_size: 128
n_layers: 3
learning_rate: 0.0008
```

使用自定义配置:
```bash
python run_recbole.py --model CFEARec --dataset Amazon_All_Beauty \
    --config_files my_cfearec_config.yaml
```

### 批量实验脚本
```bash
#!/bin/bash
# 批量测试不同参数组合

datasets=("Amazon_All_Beauty" "ml-100k" "Amazon_CDs_and_Vinyl")
weights=(0.05 0.1 0.15 0.2)

for dataset in "${datasets[@]}"; do
    for weight in "${weights[@]}"; do
        echo "Testing $dataset with weight $weight"
        python run_recbole.py --model CFEARec --dataset $dataset \
            --enable_contrastive --contrastive_weight $weight
    done
done
```

通过这些示例，您可以根据具体需求灵活使用CFEARec模型的对比学习功能！
