#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script to verify CFEARec configuration loading
"""

from recbole.config import Config
from recbole.data import create_dataset
from recbole.model.sequential_recommender.cfearec import CFEARec


def test_config_loading():
    """Test if CFEARec correctly loads configuration from yaml file"""
    
    print("🧪 Testing CFEARec Configuration Loading")
    print("=" * 50)
    
    try:
        # Create config without any overrides
        config = Config(model='CFEARec', dataset='Amazon_All_Beauty')
        print(f"✅ Config created successfully")
        
        # Check contrastive learning settings
        print(f"\n📋 Configuration Parameters:")
        print(f"   - use_contrastive_learning: {config['use_contrastive_learning'] if 'use_contrastive_learning' in config else 'Not found'}")
        print(f"   - contrastive_weight: {config['contrastive_weight'] if 'contrastive_weight' in config else 'Not found'}")
        print(f"   - contrastive_temperature: {config['contrastive_temperature'] if 'contrastive_temperature' in config else 'Not found'}")
        print(f"   - use_user_spectrum_analysis: {config['use_user_spectrum_analysis'] if 'use_user_spectrum_analysis' in config else 'Not found'}")
        print(f"   - spectrum_analysis_weight: {config['spectrum_analysis_weight'] if 'spectrum_analysis_weight' in config else 'Not found'}")
        
        # Create dataset
        dataset = create_dataset(config)
        print(f"✅ Dataset created: {len(dataset)} interactions")
        
        # Create model
        model = CFEARec(config, dataset)
        print(f"✅ CFEARec model created")
        
        # Check model's actual parameters
        print(f"\n🔧 Model Runtime Parameters:")
        print(f"   - use_contrastive_learning: {model.use_contrastive_learning}")
        print(f"   - contrastive_weight: {model.contrastive_weight}")
        print(f"   - contrastive_temperature: {model.contrastive_temperature}")
        print(f"   - use_user_spectrum_analysis: {model.use_user_spectrum_analysis}")
        print(f"   - spectrum_analysis_weight: {model.spectrum_analysis_weight}")
        
        # Check if contrastive projection layer exists
        if hasattr(model, 'contrastive_projection') and model.contrastive_projection is not None:
            print(f"✅ Contrastive projection layer created")
        else:
            print(f"❌ Contrastive projection layer not created")
        
        # Final verdict
        if model.use_contrastive_learning:
            print(f"\n🎉 SUCCESS: Contrastive learning is ENABLED from config file!")
        else:
            print(f"\n⚠️  INFO: Contrastive learning is DISABLED from config file")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = test_config_loading()
    if success:
        print("\n✅ Configuration loading test completed!")
    else:
        print("\n❌ Configuration loading test failed!")
