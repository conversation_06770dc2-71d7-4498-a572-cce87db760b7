#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CFEARec with Frequency-Aware Positional Encoding Demo
=====================================================

This script demonstrates how to use CFEARec with frequency-aware positional encoding
instead of traditional learnable positional embeddings.

Usage:
    python run_cfearec_frequency_pe.py --dataset Amazon_All_Beauty
    python run_cfearec_frequency_pe.py --dataset Amazon_All_Beauty --compare_pe_types
"""

import argparse
import sys
import os

# Add RecBole path
sys.path.append('RecBole-0708')

from recbole.quick_start import run_recbole
from recbole.utils import init_logger, init_seed


def run_cfearec_with_frequency_pe(dataset_name, compare_pe_types=False):
    """运行CFEARec模型，使用频域感知位置编码"""
    
    print("=" * 80)
    print("🚀 CFEARec with Frequency-Aware Positional Encoding")
    print("=" * 80)
    
    if compare_pe_types:
        print("📊 Running comparison between different positional encoding types...")
        
        # 1. 运行传统可学习位置嵌入版本
        print("\n" + "="*50)
        print("🔹 Running CFEARec with Learnable Position Embedding")
        print("="*50)
        
        config_dict_learnable = {
            'model': 'CFEARec',
            'dataset': dataset_name,
            
            # 基础参数
            'embedding_size': 64,
            'hidden_size': 64,
            'n_layers': 2,
            'n_heads': 2,
            'inner_size': 256,
            'hidden_dropout_prob': 0.5,
            'attn_dropout_prob': 0.5,
            'hidden_act': 'gelu',
            'layer_norm_eps': 1e-12,
            'initializer_range': 0.02,
            
            # 损失函数
            'loss_type': 'CE',
            'lmd': 0.1,
            'lmd_sem': 0.1,
            
            # 频域参数
            'global_ratio': 1.0,
            'dual_domain': True,
            'std': False,
            'spatial_ratio': 0.0,
            'fredom': False,
            'fredom_type': None,
            'topk_factor': 1,
            'use_filter': True,
            
            # 用户频谱分析
            'use_user_spectrum_analysis': True,
            'spectrum_analysis_weight': 0.05,
            'adaptive_filter_strength': 1.0,
            
            # 位置编码：使用传统可学习嵌入
            'use_frequency_aware_pe': False,
            'pe_dropout': 0.1,
            
            # 训练参数
            'epochs': 50,
            'train_batch_size': 2048,
            'eval_batch_size': 2048,
            'learning_rate': 0.001,
            'eval_step': 1,
            'stopping_step': 10,
            
            # 评估参数
            'metrics': ['Recall', 'MRR', 'NDCG', 'Hit', 'Precision'],
            'topk': [5, 10, 20],
            'valid_metric': 'MRR@10',
            'metric_decimal_place': 4,
        }
        
        print("📋 Configuration for Learnable PE:")
        print(f"   - Model: {config_dict_learnable['model']}")
        print(f"   - Dataset: {config_dict_learnable['dataset']}")
        print(f"   - use_frequency_aware_pe: {config_dict_learnable['use_frequency_aware_pe']}")
        print(f"   - use_user_spectrum_analysis: {config_dict_learnable['use_user_spectrum_analysis']}")
        
        try:
            result_learnable = run_recbole(
                model='CFEARec',
                dataset=dataset_name,
                config_dict=config_dict_learnable
            )
            print("✅ Learnable PE experiment completed successfully!")
            print(f"📈 Best valid score: {result_learnable['best_valid_score']:.4f}")
            print(f"📊 Test result: {result_learnable['test_result']}")
        except Exception as e:
            print(f"❌ Learnable PE experiment failed: {e}")
            result_learnable = None
        
        # 2. 运行频域感知位置编码版本
        print("\n" + "="*50)
        print("🔹 Running CFEARec with Frequency-Aware Position Encoding")
        print("="*50)
        
        config_dict_frequency = config_dict_learnable.copy()
        config_dict_frequency.update({
            # 位置编码：使用频域感知编码
            'use_frequency_aware_pe': True,
            'pe_dropout': 0.1,
        })
        
        print("📋 Configuration for Frequency-Aware PE:")
        print(f"   - Model: {config_dict_frequency['model']}")
        print(f"   - Dataset: {config_dict_frequency['dataset']}")
        print(f"   - use_frequency_aware_pe: {config_dict_frequency['use_frequency_aware_pe']}")
        print(f"   - pe_dropout: {config_dict_frequency['pe_dropout']}")
        print(f"   - use_user_spectrum_analysis: {config_dict_frequency['use_user_spectrum_analysis']}")
        
        try:
            result_frequency = run_recbole(
                model='CFEARec',
                dataset=dataset_name,
                config_dict=config_dict_frequency
            )
            print("✅ Frequency-Aware PE experiment completed successfully!")
            print(f"📈 Best valid score: {result_frequency['best_valid_score']:.4f}")
            print(f"📊 Test result: {result_frequency['test_result']}")
        except Exception as e:
            print(f"❌ Frequency-Aware PE experiment failed: {e}")
            result_frequency = None
        
        # 3. 比较结果
        print("\n" + "="*80)
        print("📊 COMPARISON RESULTS")
        print("="*80)
        
        if result_learnable and result_frequency:
            learnable_score = result_learnable['best_valid_score']
            frequency_score = result_frequency['best_valid_score']
            improvement = ((frequency_score - learnable_score) / learnable_score) * 100
            
            print(f"🔹 Learnable PE Score:      {learnable_score:.4f}")
            print(f"🔹 Frequency-Aware PE Score: {frequency_score:.4f}")
            print(f"🔹 Improvement:             {improvement:+.2f}%")
            
            if improvement > 0:
                print("✅ Frequency-Aware PE shows improvement!")
            else:
                print("⚠️  Learnable PE performs better in this case.")
                
            print("\n📋 Detailed Test Results:")
            print("Learnable PE:")
            for metric, value in result_learnable['test_result'].items():
                print(f"   {metric}: {value:.4f}")
            print("Frequency-Aware PE:")
            for metric, value in result_frequency['test_result'].items():
                print(f"   {metric}: {value:.4f}")
        else:
            print("❌ Cannot compare results due to experiment failures.")
    
    else:
        # 只运行频域感知位置编码版本
        print("🔹 Running CFEARec with Frequency-Aware Position Encoding only")
        
        config_dict = {
            'model': 'CFEARec',
            'dataset': dataset_name,
            
            # 基础参数
            'embedding_size': 64,
            'hidden_size': 64,
            'n_layers': 2,
            'n_heads': 2,
            'inner_size': 256,
            'hidden_dropout_prob': 0.5,
            'attn_dropout_prob': 0.5,
            'hidden_act': 'gelu',
            'layer_norm_eps': 1e-12,
            'initializer_range': 0.02,
            
            # 损失函数
            'loss_type': 'CE',
            'lmd': 0.1,
            'lmd_sem': 0.1,
            
            # 频域参数
            'global_ratio': 1.0,
            'dual_domain': True,
            'std': False,
            'spatial_ratio': 0.0,
            'fredom': False,
            'fredom_type': None,
            'topk_factor': 1,
            'use_filter': True,
            
            # 用户频谱分析
            'use_user_spectrum_analysis': True,
            'spectrum_analysis_weight': 0.05,
            'adaptive_filter_strength': 1.0,
            
            # 位置编码：使用频域感知编码
            'use_frequency_aware_pe': True,
            'pe_dropout': 0.1,
            
            # 训练参数
            'epochs': 50,
            'train_batch_size': 2048,
            'eval_batch_size': 2048,
            'learning_rate': 0.001,
            'eval_step': 1,
            'stopping_step': 10,
            
            # 评估参数
            'metrics': ['Recall', 'MRR', 'NDCG', 'Hit', 'Precision'],
            'topk': [5, 10, 20],
            'valid_metric': 'MRR@10',
            'metric_decimal_place': 4,
        }
        
        print("📋 Configuration:")
        print(f"   - Model: {config_dict['model']}")
        print(f"   - Dataset: {config_dict['dataset']}")
        print(f"   - use_frequency_aware_pe: {config_dict['use_frequency_aware_pe']}")
        print(f"   - pe_dropout: {config_dict['pe_dropout']}")
        print(f"   - use_user_spectrum_analysis: {config_dict['use_user_spectrum_analysis']}")
        
        try:
            result = run_recbole(
                model='CFEARec',
                dataset=dataset_name,
                config_dict=config_dict
            )
            print("✅ Experiment completed successfully!")
            print(f"📈 Best valid score: {result['best_valid_score']:.4f}")
            print(f"📊 Test result: {result['test_result']}")
        except Exception as e:
            print(f"❌ Experiment failed: {e}")
    
    print("\n" + "="*80)
    print("🎯 Experiment Summary")
    print("="*80)
    print("✅ CFEARec with Frequency-Aware Positional Encoding experiment completed!")
    print("📚 Key Features Demonstrated:")
    print("   🔹 Frequency-aware positional encoding with sinusoidal base")
    print("   🔹 Dynamic frequency modulation based on sequence characteristics")
    print("   🔹 Adaptive weighting between time-domain and frequency-domain features")
    print("   🔹 Integration with user spectrum analysis and adaptive filtering")
    print("="*80)


def main():
    parser = argparse.ArgumentParser(description='CFEARec with Frequency-Aware Positional Encoding')
    parser.add_argument('--dataset', type=str, default='Amazon_All_Beauty',
                       help='Dataset name (default: Amazon_All_Beauty)')
    parser.add_argument('--compare_pe_types', action='store_true',
                       help='Compare learnable PE vs frequency-aware PE')
    
    args = parser.parse_args()
    
    # 初始化随机种子
    init_seed(2024, reproducibility=True)
    
    # 运行实验
    run_cfearec_with_frequency_pe(args.dataset, args.compare_pe_types)


if __name__ == '__main__':
    main()
