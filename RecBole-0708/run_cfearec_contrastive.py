#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CFEARec with Contrastive Learning Runner
========================================

This script demonstrates how to run CFEARec model with contrastive learning enabled.
The contrastive learning mechanism compares original sequences with filtered sequences
to improve the model's representation learning capability.

Usage:
    # Run with contrastive learning enabled
    python run_cfearec_contrastive.py --dataset Amazon_Baby_Products --enable_contrastive

    # Run without contrastive learning (standard CFEARec)
    python run_cfearec_contrastive.py --dataset Amazon_Baby_Products

    # Run with custom contrastive learning parameters
    python run_cfearec_contrastive.py --dataset Amazon_Baby_Products --enable_contrastive \
        --contrastive_weight 0.2 --contrastive_temperature 0.05
"""

import argparse
import yaml
import os
from recbole.quick_start import run


def create_config_dict(args):
    """Create configuration dictionary based on command line arguments"""
    config_dict = {}
    
    # Basic model configuration
    config_dict.update({
        'model': 'CFEARec',
        'dataset': args.dataset,
        
        # Basic parameters
        'embedding_size': 64,
        'hidden_size': 64,
        'n_layers': 2,
        'n_heads': 2,
        'inner_size': 256,
        'hidden_dropout_prob': 0.5,
        'attn_dropout_prob': 0.5,
        'hidden_act': 'gelu',
        'layer_norm_eps': 1e-12,
        'initializer_range': 0.02,
        
        # Loss function
        'loss_type': 'CE',
        'lmd': 0.1,
        'lmd_sem': 0.1,
        
        # Frequency domain
        'global_ratio': 1.0,
        'dual_domain': True,
        'std': False,
        'spatial_ratio': 0.0,
        'fredom': False,
        'fredom_type': None,
        'topk_factor': 1,
        'use_filter': True,
        
        # User spectrum analysis
        'use_user_spectrum_analysis': True,
        'spectrum_analysis_weight': 0.05,
        'adaptive_filter_strength': 1.0,
        
        # User type parameters
        'user_type_entropy_weight': 0.1,
        'user_type_balance_weight': 0.01,
        
        # Filter parameters
        'lpf_strength': 1.0,
        'hpf_strength': 1.0,
        'bpf_strength': 1.0,
        'bsf_strength': 1.0,
        'apf_strength': 1.0,
        
        # Training parameters
        'train_batch_size': args.batch_size,
        'learning_rate': args.learning_rate,
        'epochs': args.epochs,
        'eval_step': 1,
        'stopping_step': 10,
        
        # Evaluation
        'metrics': ['Recall', 'NDCG'],
        'topk': [5, 10, 20],
        'valid_metric': 'NDCG@10'
    })
    
    # Contrastive learning configuration
    if args.enable_contrastive:
        config_dict.update({
            'use_contrastive_learning': True,
            'contrastive_weight': args.contrastive_weight,
            'contrastive_temperature': args.contrastive_temperature
        })
        print(f"🔥 Contrastive Learning ENABLED")
        print(f"   - Contrastive Weight: {args.contrastive_weight}")
        print(f"   - Temperature: {args.contrastive_temperature}")
    else:
        config_dict.update({
            'use_contrastive_learning': False
        })
        print("📊 Running standard CFEARec (without contrastive learning)")
    
    return config_dict


def main():
    parser = argparse.ArgumentParser(description='Run CFEARec with optional contrastive learning')
    
    # Basic arguments
    parser.add_argument('--dataset', '-d', type=str, default='Amazon_Baby_Products',
                       help='Dataset name')
    parser.add_argument('--config_files', type=str, default=None,
                       help='Config files (optional)')
    
    # Contrastive learning arguments
    parser.add_argument('--enable_contrastive', action='store_true',
                       help='Enable contrastive learning between original and filtered sequences')
    parser.add_argument('--contrastive_weight', type=float, default=0.1,
                       help='Weight for contrastive learning loss (default: 0.1)')
    parser.add_argument('--contrastive_temperature', type=float, default=0.07,
                       help='Temperature parameter for InfoNCE loss (default: 0.07)')
    
    # Training arguments
    parser.add_argument('--batch_size', type=int, default=256,
                       help='Training batch size (default: 256)')
    parser.add_argument('--learning_rate', type=float, default=0.001,
                       help='Learning rate (default: 0.001)')
    parser.add_argument('--epochs', type=int, default=300,
                       help='Number of training epochs (default: 300)')
    
    # Distributed training arguments
    parser.add_argument('--nproc', type=int, default=1,
                       help='Number of processes')
    parser.add_argument('--ip', type=str, default='localhost',
                       help='IP of master node')
    parser.add_argument('--port', type=str, default='5678',
                       help='Port of master node')
    parser.add_argument('--world_size', type=int, default=-1,
                       help='Total number of jobs')
    parser.add_argument('--group_offset', type=int, default=0,
                       help='Global rank offset of this group')
    
    args = parser.parse_args()
    
    # Print configuration
    print("=" * 60)
    print("🚀 CFEARec Model Runner")
    print("=" * 60)
    print(f"📊 Dataset: {args.dataset}")
    print(f"🔧 Batch Size: {args.batch_size}")
    print(f"📈 Learning Rate: {args.learning_rate}")
    print(f"🔄 Epochs: {args.epochs}")
    
    # Create configuration
    config_dict = create_config_dict(args)
    
    # Handle config files
    config_file_list = None
    if args.config_files:
        config_file_list = args.config_files.strip().split(" ")
        print(f"📄 Config Files: {config_file_list}")
    
    print("=" * 60)
    print("🏃 Starting training...")
    print("=" * 60)
    
    # Run the model
    try:
        run(
            model='CFEARec',
            dataset=args.dataset,
            config_dict=config_dict,
            config_file_list=config_file_list,
            nproc=args.nproc,
            world_size=args.world_size,
            ip=args.ip,
            port=args.port,
            group_offset=args.group_offset,
        )
        print("✅ Training completed successfully!")
    except Exception as e:
        print(f"❌ Training failed with error: {e}")
        raise


if __name__ == "__main__":
    main()
