#!/usr/bin/env python3
"""
测试run_seq.py中的结果记录功能
"""
from run_seq import parse_log_and_record_result
import glob

def test_record_function():
    # 找到最新的日志文件
    log_files = glob.glob('log/CLF4SRec/ml-100k/*/log.txt')
    if not log_files:
        print("未找到日志文件")
        return
    
    # 按时间戳排序，取最新的
    log_files.sort(key=lambda x: x.split('/')[-2])
    latest_log = log_files[-1]
    
    print(f"测试日志文件: {latest_log}")
    
    # 测试记录功能
    success = parse_log_and_record_result(latest_log, 'CLF4SRec', 'ml-100k', 'result/test_results.csv')
    
    if success:
        print("✓ 记录功能测试成功")
        # 显示CSV内容
        with open('result/test_results.csv', 'r') as f:
            print("\n=== CSV内容 ===")
            print(f.read())
    else:
        print("✗ 记录功能测试失败")

if __name__ == "__main__":
    test_record_function()
