#!/usr/bin/env python3
import csv
import re
import json
from datetime import datetime

def clean_ansi_codes(text):
    """移除ANSI颜色代码"""
    ansi_escape = re.compile(r'\x1B(?:[@-Z\\-_]|\[[0-?]*[ -/]*[@-~])')
    return ansi_escape.sub('', text)

def test_parse_log():
    log_file = 'log/CLF4SRec/ml-100k/bs256-lmd0.1-sem0.1-us_x-Jul-20-2025_16-47-49-lr0.001-l20-tau1-dot-DPh0.5-DPa0.5/log.txt'
    
    with open(log_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    clean_content = clean_ansi_codes(content)
    
    result = {}
    
    # 提取时间戳
    timestamp_match = re.search(r'(\w{3} \d{2} \w{3} \d{4} \d{2}:\d{2}:\d{2})', clean_content)
    if timestamp_match:
        result['run_timestamp'] = timestamp_match.group(1)
        print(f"时间戳: {result['run_timestamp']}")
    
    # 提取数据集统计信息
    user_match = re.search(r'The number of users: (\d+)', clean_content)
    item_match = re.search(r'The number of items: (\d+)', clean_content)
    interaction_match = re.search(r'The number of inters: (\d+)', clean_content)
    
    if user_match and item_match and interaction_match:
        num_users = int(user_match.group(1))
        num_items = int(item_match.group(1))
        num_interactions = int(interaction_match.group(1))
        result['num_users'] = num_users
        result['num_items'] = num_items
        result['avg_interactions'] = round(num_interactions / num_users, 2)
        print(f"用户数: {num_users}, 物品数: {num_items}, 平均交互数: {result['avg_interactions']}")
    
    # 提取测试集结果
    test_result_match = re.search(r'test result: ({[^}]+})', clean_content)
    if test_result_match:
        try:
            test_str = test_result_match.group(1)
            test_str = test_str.replace("'", '"')
            test_results = json.loads(test_str)
            
            print("测试结果:")
            for key, value in test_results.items():
                result[key] = round(value, 4)
                print(f"  {key}: {result[key]}")
        
        except json.JSONDecodeError as e:
            print(f"无法解析测试结果: {e}")
    
    # 写入CSV
    csv_path = "result/results.csv"
    
    # 创建CSV文件和表头
    with open(csv_path, 'w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        headers = [
            'model_name', 'dataset_name', 'num_users', 'num_items', 
            'avg_interactions', 'run_timestamp',
            'ndcg@5', 'ndcg@10', 'ndcg@20',
            'hit@5', 'hit@10', 'hit@20',
            'mrr@5', 'mrr@10', 'mrr@20',
            'recall@5', 'recall@10', 'recall@20',
            'precision@5', 'precision@10', 'precision@20',
            'map@5', 'map@10', 'map@20'
        ]
        writer.writerow(headers)
    
    # 写入数据
    with open(csv_path, 'a', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        row = [
            'CLF4SRec',  # model_name
            'ml-100k',   # dataset_name
            result.get('num_users', ''),
            result.get('num_items', ''),
            result.get('avg_interactions', ''),
            result.get('run_timestamp', ''),
            result.get('ndcg@5', ''),
            result.get('ndcg@10', ''),
            result.get('ndcg@20', ''),
            result.get('hit@5', ''),
            result.get('hit@10', ''),
            result.get('hit@20', ''),
            result.get('mrr@5', ''),
            result.get('mrr@10', ''),
            result.get('mrr@20', ''),
            result.get('recall@5', ''),
            result.get('recall@10', ''),
            result.get('recall@20', ''),
            result.get('precision@5', ''),
            result.get('precision@10', ''),
            result.get('precision@20', ''),
            result.get('map@5', ''),
            result.get('map@10', ''),
            result.get('map@20', '')
        ]
        writer.writerow(row)
    
    print(f"\n结果已写入 {csv_path}")

if __name__ == "__main__":
    test_parse_log()
