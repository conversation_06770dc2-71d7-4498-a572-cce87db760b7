# CLF4SRec
## Model
Our model CLF4SRec is implemented based on the [RecBole](https://github.com/RUCAIBox/RecBole). 

Both the processing of the dataset and the metrics calculation follow the implementation of RecBole.

CLF4SRec in /recbole/model/sequential_recommender/clf4srec.py
### Usage
We provide the script main_run.py to run the model
## Citation
If you use this code, please cite the pape
```
@article{CLF4SRec,
title = {Contrastive Learning with Frequency Domain for Sequential Recommendation},
journal = {Applied Soft Computing},
pages = {110481},
year = {2023},
issn = {1568-4946},
doi = {https://doi.org/10.1016/j.asoc.2023.110481},
url = {https://www.sciencedirect.com/science/article/pii/S1568494623004994},
author = {<PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> and <PERSON><PERSON><PERSON>}
}
```
# Credit
This repo is based on [RecBole](https://github.com/RUCAIBox/RecBole) and [DuoRec](https://github.com/RuihongQiu/DuoRec)
