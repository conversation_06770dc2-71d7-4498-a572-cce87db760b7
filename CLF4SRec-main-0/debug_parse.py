#!/usr/bin/env python3
import re
import json

def debug_parse_log():
    log_file = 'log/CLF4SRec/ml-100k/bs256-lmd0.1-sem0.1-us_x-Jul-20-2025_16-47-49-lr0.001-l20-tau1-dot-DPh0.5-DPa0.5/log.txt'
    
    with open(log_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 清理ANSI代码
    ansi_escape = re.compile(r'\x1B(?:[@-Z\\-_]|\[[0-?]*[ -/]*[@-~])')
    clean_content = ansi_escape.sub('', content)
    
    print("=== 调试数据集统计信息解析 ===")
    
    # 尝试不同的用户数匹配模式
    patterns = [
        r'The number of users: (\d+)',
        r'users: (\d+)',
        r'(\d+) users',
        r'user.*?(\d+)',
    ]
    
    for pattern in patterns:
        match = re.search(pattern, clean_content, re.IGNORECASE)
        if match:
            print(f"✓ 用户数匹配成功: {pattern} -> {match.group(1)}")
            break
        else:
            print(f"✗ 用户数匹配失败: {pattern}")
    
    # 尝试不同的物品数匹配模式
    patterns = [
        r'The number of items: (\d+)',
        r'items: (\d+)',
        r'(\d+) items',
        r'item.*?(\d+)',
    ]
    
    for pattern in patterns:
        match = re.search(pattern, clean_content, re.IGNORECASE)
        if match:
            print(f"✓ 物品数匹配成功: {pattern} -> {match.group(1)}")
            break
        else:
            print(f"✗ 物品数匹配失败: {pattern}")
    
    # 尝试不同的交互数匹配模式
    patterns = [
        r'The number of inters: (\d+)',
        r'inters: (\d+)',
        r'(\d+) inters',
        r'interaction.*?(\d+)',
    ]
    
    for pattern in patterns:
        match = re.search(pattern, clean_content, re.IGNORECASE)
        if match:
            print(f"✓ 交互数匹配成功: {pattern} -> {match.group(1)}")
            break
        else:
            print(f"✗ 交互数匹配失败: {pattern}")
    
    print("\n=== 调试测试结果解析 ===")
    
    # 尝试不同的测试结果匹配模式
    patterns = [
        r'test result: ({.+?})',
        r'test result: ({[^}]+})',
        r'result: ({.+?})',
    ]
    
    for pattern in patterns:
        match = re.search(pattern, clean_content, re.DOTALL)
        if match:
            print(f"✓ 测试结果匹配成功: {pattern}")
            test_str = match.group(1)
            print(f"原始结果: {test_str[:100]}...")
            try:
                test_str_clean = test_str.replace("'", '"')
                test_results = json.loads(test_str_clean)
                print(f"解析成功，包含 {len(test_results)} 个指标")
                for key, value in list(test_results.items())[:5]:
                    print(f"  {key}: {value}")
                break
            except json.JSONDecodeError as e:
                print(f"JSON解析失败: {e}")
        else:
            print(f"✗ 测试结果匹配失败: {pattern}")
    
    # 手动查找包含数字的行
    print("\n=== 手动查找关键信息 ===")
    lines = clean_content.split('\n')
    
    print("包含'users'的行:")
    for i, line in enumerate(lines):
        if 'users' in line.lower() and any(c.isdigit() for c in line):
            print(f"  {i}: {line.strip()}")
    
    print("\n包含'items'的行:")
    for i, line in enumerate(lines):
        if 'items' in line.lower() and any(c.isdigit() for c in line):
            print(f"  {i}: {line.strip()}")
    
    print("\n包含'test result'的行:")
    for i, line in enumerate(lines):
        if 'test result' in line.lower():
            print(f"  {i}: {line.strip()[:100]}...")

if __name__ == "__main__":
    debug_parse_log()
