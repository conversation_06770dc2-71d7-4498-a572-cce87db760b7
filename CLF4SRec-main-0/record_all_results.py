#!/usr/bin/env python3
"""
记录所有现有实验结果到CSV文件
"""
import csv
import re
import json
import glob
import os
from datetime import datetime

def clean_ansi_codes(text):
    """移除ANSI颜色代码"""
    ansi_escape = re.compile(r'\x1B(?:[@-Z\\-_]|\[[0-?]*[ -/]*[@-~])')
    return ansi_escape.sub('', text)

def parse_log_file(log_file_path):
    """解析单个日志文件"""
    result = {}
    
    try:
        with open(log_file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        clean_content = clean_ansi_codes(content)
        
        # 提取模型和数据集名称
        path_parts = log_file_path.split('/')
        if len(path_parts) >= 3:
            result['model_name'] = path_parts[1]  # CLF4SRec
            result['dataset_name'] = path_parts[2]  # ml-100k
        
        # 提取时间戳
        timestamp_match = re.search(r'(\w{3} \d{2} \w{3} \d{4} \d{2}:\d{2}:\d{2})', clean_content)
        if timestamp_match:
            result['run_timestamp'] = timestamp_match.group(1)
        else:
            result['run_timestamp'] = datetime.now().strftime('%a %d %b %Y %H:%M:%S')
        
        # 提取数据集统计信息
        user_match = re.search(r'The number of users: (\d+)', clean_content)
        item_match = re.search(r'The number of items: (\d+)', clean_content)
        interaction_match = re.search(r'The number of inters: (\d+)', clean_content)

        # 如果没找到，尝试其他格式
        if not user_match:
            user_match = re.search(r'users: (\d+)', clean_content)
        if not item_match:
            item_match = re.search(r'items: (\d+)', clean_content)
        if not interaction_match:
            interaction_match = re.search(r'inters: (\d+)', clean_content)
        
        if user_match and item_match and interaction_match:
            num_users = int(user_match.group(1))
            num_items = int(item_match.group(1))
            num_interactions = int(interaction_match.group(1))
            result['num_users'] = num_users
            result['num_items'] = num_items
            result['avg_interactions'] = round(num_interactions / num_users, 2)
        
        # 提取测试集结果
        test_result_match = re.search(r'test result: ({.+?})', clean_content, re.DOTALL)
        if test_result_match:
            try:
                test_str = test_result_match.group(1)
                test_str = test_str.replace("'", '"')
                test_results = json.loads(test_str)
                
                metrics = ['ndcg@5', 'ndcg@10', 'ndcg@20', 
                          'hit@5', 'hit@10', 'hit@20',
                          'mrr@5', 'mrr@10', 'mrr@20',
                          'recall@5', 'recall@10', 'recall@20',
                          'precision@5', 'precision@10', 'precision@20',
                          'map@5', 'map@10', 'map@20']
                
                for metric in metrics:
                    if metric in test_results:
                        result[metric] = round(test_results[metric], 4)
            
            except json.JSONDecodeError:
                print(f"无法解析测试结果: {test_str}")
        
        return result
        
    except Exception as e:
        print(f"解析日志文件 {log_file_path} 时出错: {e}")
        return None

def create_results_csv(csv_path="result/results.csv"):
    """创建结果CSV文件"""
    os.makedirs(os.path.dirname(csv_path), exist_ok=True)
    
    # 查找所有日志文件
    log_files = glob.glob('log/*/*/*/log.txt')
    print(f"找到 {len(log_files)} 个日志文件")
    
    if not log_files:
        print("未找到任何日志文件")
        return
    
    # 创建CSV文件和表头
    with open(csv_path, 'w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        headers = [
            'model_name', 'dataset_name', 'num_users', 'num_items', 
            'avg_interactions', 'run_timestamp',
            'ndcg@5', 'ndcg@10', 'ndcg@20',
            'hit@5', 'hit@10', 'hit@20',
            'mrr@5', 'mrr@10', 'mrr@20',
            'recall@5', 'recall@10', 'recall@20',
            'precision@5', 'precision@10', 'precision@20',
            'map@5', 'map@10', 'map@20'
        ]
        writer.writerow(headers)
    
    # 处理每个日志文件
    successful_records = 0
    for log_file in sorted(log_files):
        print(f"处理: {log_file}")
        result = parse_log_file(log_file)
        
        if result:
            # 写入数据
            with open(csv_path, 'a', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                row = [
                    result.get('model_name', ''),
                    result.get('dataset_name', ''),
                    result.get('num_users', ''),
                    result.get('num_items', ''),
                    result.get('avg_interactions', ''),
                    result.get('run_timestamp', ''),
                    result.get('ndcg@5', ''),
                    result.get('ndcg@10', ''),
                    result.get('ndcg@20', ''),
                    result.get('hit@5', ''),
                    result.get('hit@10', ''),
                    result.get('hit@20', ''),
                    result.get('mrr@5', ''),
                    result.get('mrr@10', ''),
                    result.get('mrr@20', ''),
                    result.get('recall@5', ''),
                    result.get('recall@10', ''),
                    result.get('recall@20', ''),
                    result.get('precision@5', ''),
                    result.get('precision@10', ''),
                    result.get('precision@20', ''),
                    result.get('map@5', ''),
                    result.get('map@10', ''),
                    result.get('map@20', '')
                ]
                writer.writerow(row)
            
            successful_records += 1
            print(f"  ✓ 记录成功 - {result.get('model_name', 'Unknown')}/{result.get('dataset_name', 'Unknown')}")
            if 'hit@10' in result:
                print(f"    Hit@10: {result['hit@10']}")
        else:
            print(f"  ✗ 记录失败")
    
    print(f"\n=== 记录完成 ===")
    print(f"成功记录: {successful_records}/{len(log_files)} 个实验")
    print(f"结果文件: {csv_path}")

if __name__ == "__main__":
    create_results_csv()
