# @Time   : 2020/7/7
# <AUTHOR> <PERSON><PERSON><PERSON>
# @Email  : <EMAIL>

# UPDATE
# @Time   : 2020/9/9, 2020/9/16
# <AUTHOR> <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>
# @email  : <EMAIL>, ch<PERSON><PERSON><PERSON><PERSON>@ruc.edu.cn

"""
recbole.data.dataloader.context_dataloader
################################################
"""

from recbole.data.dataloader.general_dataloader import GeneralData<PERSON>oader, GeneralNegSampleDataLoader, \
    GeneralFullDataLoader


class ContextDataLoader(GeneralDataLoader):
    """:class:`ContextDataLoader` is inherit from
    :class:`~recbole.data.dataloader.general_dataloader.GeneralDataLoader`,
    and didn't add/change anything at all.
    """
    pass


class ContextNegSampleDataLoader(GeneralNegSampleDataLoader):
    """:class:`ContextNegSampleData<PERSON>oa<PERSON>` is inherit from
    :class:`~recbole.data.dataloader.general_dataloader.GeneralNegSampleDataLoader`,
    and didn't add/change anything at all.
    """
    pass


class ContextFullDataLoader(GeneralFullDataLoader):
    """:class:`ContextFullDataLoader` is inherit from
    :class:`~recbole.data.dataloader.general_dataloader.GeneralFullDataLoader`,
    and didn't add/change anything at all.
    """
    pass
