#!/usr/bin/env python3
"""
查看实验结果的便捷脚本
"""
import pandas as pd
import os

def view_results(csv_path="result/results.csv"):
    """查看实验结果"""
    if not os.path.exists(csv_path):
        print(f"结果文件不存在: {csv_path}")
        print("请先运行实验或使用 python record_all_results.py 记录现有结果")
        return
    
    try:
        # 读取CSV文件
        df = pd.read_csv(csv_path)
        
        if df.empty:
            print("结果文件为空")
            return
        
        print("=== 实验结果总览 ===")
        print(f"总实验数: {len(df)}")
        print(f"模型数: {df['model_name'].nunique()}")
        print(f"数据集数: {df['dataset_name'].nunique()}")
        
        print("\n=== 详细结果 ===")
        
        # 设置显示选项
        pd.set_option('display.max_columns', None)
        pd.set_option('display.width', None)
        pd.set_option('display.max_colwidth', 20)
        
        # 选择要显示的关键列
        key_columns = ['model_name', 'dataset_name', 'num_users', 'num_items', 
                      'avg_interactions', 'run_timestamp', 
                      'hit@5', 'hit@10', 'hit@20', 
                      'ndcg@5', 'ndcg@10', 'ndcg@20']
        
        # 只显示存在的列
        available_columns = [col for col in key_columns if col in df.columns]
        
        print(df[available_columns].to_string(index=False))
        
        # 显示最佳结果
        if 'hit@10' in df.columns and not df['hit@10'].isna().all():
            best_hit10_idx = df['hit@10'].idxmax()
            best_hit10 = df.loc[best_hit10_idx]
            
            print(f"\n=== 最佳Hit@10结果 ===")
            print(f"模型: {best_hit10['model_name']}")
            print(f"数据集: {best_hit10['dataset_name']}")
            print(f"时间: {best_hit10['run_timestamp']}")
            print(f"Hit@10: {best_hit10['hit@10']:.4f}")
            if 'ndcg@10' in df.columns:
                print(f"NDCG@10: {best_hit10['ndcg@10']:.4f}")
        
        print(f"\n结果文件位置: {csv_path}")
        
    except Exception as e:
        print(f"读取结果文件时出错: {e}")

def view_results_simple(csv_path="result/results.csv"):
    """简单查看结果（不依赖pandas）"""
    if not os.path.exists(csv_path):
        print(f"结果文件不存在: {csv_path}")
        return
    
    try:
        with open(csv_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        if len(lines) <= 1:
            print("结果文件为空或只有表头")
            return
        
        print("=== 实验结果 ===")
        print(f"总实验数: {len(lines) - 1}")
        
        # 显示表头
        header = lines[0].strip().split(',')
        print("\n表头:")
        for i, col in enumerate(header):
            print(f"{i+1:2d}. {col}")
        
        print("\n=== 最近5次实验结果 ===")
        for i, line in enumerate(lines[-5:] if len(lines) > 5 else lines[1:]):
            if i == 0 and len(lines) > 5:
                continue  # 跳过表头
            values = line.strip().split(',')
            print(f"\n实验 {i+1}:")
            for j, (col, val) in enumerate(zip(header, values)):
                if val and col in ['model_name', 'dataset_name', 'run_timestamp', 'hit@10', 'ndcg@10']:
                    print(f"  {col}: {val}")
        
        print(f"\n完整结果文件: {csv_path}")
        
    except Exception as e:
        print(f"读取结果文件时出错: {e}")

if __name__ == "__main__":
    # 尝试使用pandas，如果失败则使用简单版本
    try:
        import pandas as pd
        view_results()
    except ImportError:
        print("未安装pandas，使用简单版本显示结果")
        view_results_simple()
