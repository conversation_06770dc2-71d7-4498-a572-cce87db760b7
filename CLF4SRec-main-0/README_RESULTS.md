# CLF4SRec 实验结果自动记录系统

## 📋 概述

本系统已经集成了自动实验结果记录功能，每次运行模型后会自动将结果保存到 `result/results.csv` 文件中。

## 🚀 使用方法

### 1. 运行模型（自动记录结果）

```bash
# 基本运行（会自动记录结果）
python run_seq.py --model CLF4SRec --dataset ml-100k

# 带参数运行
python run_seq.py --model CLF4SRec --dataset ml-100k --config_files seq.yaml
```

运行完成后，结果会自动保存到 `result/results.csv`

### 2. 查看实验结果

```bash
# 查看所有实验结果
python view_results.py

# 或直接查看CSV文件
cat result/results.csv
```

### 3. 记录现有的实验结果

如果有之前运行的实验但没有记录，可以使用：

```bash
# 记录所有现有的日志文件结果
python record_all_results.py
```

## 📊 记录的信息

每次实验会记录以下信息：

### 基本信息
- **model_name**: 模型名称（如 CLF4SRec）
- **dataset_name**: 数据集名称（如 ml-100k）
- **num_users**: 用户数量
- **num_items**: 物品数量
- **avg_interactions**: 平均交互数量
- **run_timestamp**: 运行时间戳

### 评估指标
- **NDCG**: @5, @10, @20
- **Hit**: @5, @10, @20
- **MRR**: @5, @10, @20
- **Recall**: @5, @10, @20
- **Precision**: @5, @10, @20
- **MAP**: @5, @10, @20

## 📈 当前实验结果

### MovieLens-100K 数据集统计
- **用户数**: 944
- **物品数**: 1,350
- **平均交互数**: 105.18

### CLF4SRec 最佳结果
- **Hit@10**: 9.33%
- **NDCG@10**: 4.52%
- **Hit@20**: 16.01%
- **NDCG@20**: 6.19%

## 🔧 文件说明

- `run_seq.py`: 主运行脚本（已集成自动记录功能）
- `record_all_results.py`: 记录所有现有实验结果
- `view_results.py`: 查看实验结果的便捷脚本
- `result/results.csv`: 实验结果CSV文件
- `clean_log.py`: 清理日志文件中的ANSI颜色代码

## 💡 使用技巧

1. **批量实验**: 可以连续运行多个实验，每次结果都会自动追加到CSV文件中

2. **参数对比**: 通过修改 `seq.yaml` 中的参数进行多次实验，便于对比不同参数的效果

3. **结果分析**: 使用 `python view_results.py` 快速查看最佳结果和实验概况

4. **数据备份**: 定期备份 `result/results.csv` 文件，避免数据丢失

## 🎯 示例工作流程

```bash
# 1. 运行基础实验
python run_seq.py --model CLF4SRec --dataset ml-100k

# 2. 修改参数（如调整学习率）
# 编辑 seq.yaml 文件

# 3. 运行新实验
python run_seq.py --model CLF4SRec --dataset ml-100k

# 4. 查看所有结果对比
python view_results.py

# 5. 查看详细CSV数据
cat result/results.csv
```

## 📝 注意事项

- 每次运行都会在CSV文件中添加新行，不会覆盖之前的结果
- 如果实验中途失败，可能不会记录结果
- 确保有足够的磁盘空间存储日志和结果文件
- 建议定期清理旧的日志文件以节省空间

## 🔍 故障排除

如果自动记录功能不工作：

1. 检查 `result/` 目录是否存在写入权限
2. 确认实验是否成功完成（有完整的测试结果）
3. 手动运行 `python record_all_results.py` 记录现有结果
4. 查看终端输出中的错误信息
