import argparse
import os
import glob
import csv
import re
import json
from datetime import datetime

from recbole.quick_start import run_recbole


def ensure_results_csv(csv_path="result/results.csv"):
    """确保CSV文件存在，如果不存在则创建并写入表头"""
    if not os.path.exists(csv_path):
        os.makedirs(os.path.dirname(csv_path), exist_ok=True)
        with open(csv_path, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            headers = [
                'model_name', 'dataset_name', 'num_users', 'num_items',
                'avg_interactions', 'run_timestamp',
                'ndcg@5', 'ndcg@10', 'ndcg@20',
                'hit@5', 'hit@10', 'hit@20',
                'mrr@5', 'mrr@10', 'mrr@20',
                'recall@5', 'recall@10', 'recall@20',
                'precision@5', 'precision@10', 'precision@20',
                'map@5', 'map@10', 'map@20'
            ]
            writer.writerow(headers)


def clean_ansi_codes(text):
    """移除ANSI颜色代码"""
    ansi_escape = re.compile(r'\x1B(?:[@-Z\\-_]|\[[0-?]*[ -/]*[@-~])')
    return ansi_escape.sub('', text)


def parse_log_and_record_result(log_file_path, model_name, dataset_name, csv_path="result/results.csv"):
    """解析日志文件并记录结果到CSV"""
    result = {'model_name': model_name, 'dataset_name': dataset_name}

    try:
        with open(log_file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # 清理ANSI颜色代码
        clean_content = clean_ansi_codes(content)

        # 提取时间戳
        timestamp_match = re.search(r'(\w{3} \d{2} \w{3} \d{4} \d{2}:\d{2}:\d{2})', clean_content)
        if timestamp_match:
            result['run_timestamp'] = timestamp_match.group(1)
        else:
            result['run_timestamp'] = datetime.now().strftime('%a %d %b %Y %H:%M:%S')

        # 提取数据集统计信息
        user_match = re.search(r'The number of users: (\d+)', clean_content)
        item_match = re.search(r'The number of items: (\d+)', clean_content)
        interaction_match = re.search(r'The number of inters: (\d+)', clean_content)

        if user_match and item_match and interaction_match:
            num_users = int(user_match.group(1))
            num_items = int(item_match.group(1))
            num_interactions = int(interaction_match.group(1))
            result['num_users'] = num_users
            result['num_items'] = num_items
            result['avg_interactions'] = round(num_interactions / num_users, 2)

        # 提取测试集结果
        test_result_match = re.search(r'test result: ({[^}]+})', clean_content)
        if test_result_match:
            try:
                test_str = test_result_match.group(1)
                test_str = test_str.replace("'", '"')
                test_results = json.loads(test_str)

                metrics = ['ndcg@5', 'ndcg@10', 'ndcg@20',
                          'hit@5', 'hit@10', 'hit@20',
                          'mrr@5', 'mrr@10', 'mrr@20',
                          'recall@5', 'recall@10', 'recall@20',
                          'precision@5', 'precision@10', 'precision@20',
                          'map@5', 'map@10', 'map@20']

                for metric in metrics:
                    if metric in test_results:
                        result[metric] = round(test_results[metric], 4)

            except json.JSONDecodeError:
                print(f"无法解析测试结果: {test_str}")

        # 确保CSV文件存在
        ensure_results_csv(csv_path)

        # 写入CSV文件
        with open(csv_path, 'a', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            row = [
                result.get('model_name', ''),
                result.get('dataset_name', ''),
                result.get('num_users', ''),
                result.get('num_items', ''),
                result.get('avg_interactions', ''),
                result.get('run_timestamp', ''),
                result.get('ndcg@5', ''),
                result.get('ndcg@10', ''),
                result.get('ndcg@20', ''),
                result.get('hit@5', ''),
                result.get('hit@10', ''),
                result.get('hit@20', ''),
                result.get('mrr@5', ''),
                result.get('mrr@10', ''),
                result.get('mrr@20', ''),
                result.get('recall@5', ''),
                result.get('recall@10', ''),
                result.get('recall@20', ''),
                result.get('precision@5', ''),
                result.get('precision@10', ''),
                result.get('precision@20', ''),
                result.get('map@5', ''),
                result.get('map@10', ''),
                result.get('map@20', '')
            ]
            writer.writerow(row)

        print(f"\n=== 实验结果已自动记录 ===")
        print(f"模型: {model_name}, 数据集: {dataset_name}")
        print(f"用户数: {result.get('num_users', 'N/A')}, 物品数: {result.get('num_items', 'N/A')}, 平均交互: {result.get('avg_interactions', 'N/A')}")
        print(f"结果已保存到: {csv_path}")
        if 'hit@10' in result:
            print(f"Hit@10: {result['hit@10']}")
        if 'ndcg@10' in result:
            print(f"NDCG@10: {result['ndcg@10']}")

        return True

    except Exception as e:
        print(f"记录结果时出错: {e}")
        return False


if __name__ == '__main__':

    parser = argparse.ArgumentParser()
    parser.add_argument('--model', '-m', type=str, default='CLF4SRec', help='name of models')
    parser.add_argument('--dataset', '-d', type=str, default='ml-100k', help='name of datasets')
    parser.add_argument('--config_files', type=str, default='seq.yaml', help='config files')

    args, _ = parser.parse_known_args()

    config_file_list = args.config_files.strip().split(' ') if args.config_files else None

    # 记录运行前的日志文件数量
    log_pattern = f"log/{args.model}/{args.dataset}/*/log.txt"
    existing_logs = glob.glob(log_pattern)
    existing_count = len(existing_logs)

    try:
        # 运行模型
        run_recbole(model=args.model, dataset=args.dataset, config_file_list=config_file_list)

        # 运行完成后，查找新生成的日志文件
        new_logs = glob.glob(log_pattern)

        if len(new_logs) > existing_count:
            # 找到最新的日志文件（按修改时间排序）
            new_logs.sort(key=os.path.getmtime, reverse=True)
            latest_log = new_logs[0]

            print(f"\n=== 自动记录实验结果 ===")
            print(f"最新日志文件: {latest_log}")

            # 记录结果到CSV
            parse_log_and_record_result(latest_log, args.model, args.dataset)

        else:
            print("未找到新的日志文件，可能训练未成功完成")

    except Exception as e:
        print(f"运行过程中出现错误: {e}")
        # 即使出错也尝试记录现有结果（如果有的话）
        try:
            all_logs = glob.glob(log_pattern)
            if all_logs:
                all_logs.sort(key=os.path.getmtime, reverse=True)
                latest_log = all_logs[0]
                parse_log_and_record_result(latest_log, args.model, args.dataset)
        except:
            pass
