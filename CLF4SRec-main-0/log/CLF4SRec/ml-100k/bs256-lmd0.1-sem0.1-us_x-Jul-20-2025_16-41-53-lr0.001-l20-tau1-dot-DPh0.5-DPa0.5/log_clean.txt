Sun 20 Jul 2025 16:41:53 INFO  
General Hyper Parameters:
gpu_id = 0
use_gpu = True
seed = 2020
state = INFO
reproducibility = True
data_path = /home_sda1/ghh/RS/CLF4SRec-main-0/recbole/config/../dataset_example/ml-100k
show_progress = True

Training Hyper Parameters:
checkpoint_dir = saved
epochs = 50
train_batch_size = 256
learner = adam
learning_rate = 0.001
training_neg_sample_num = 0
training_neg_sample_distribution = uniform
eval_step = 1
stopping_step = 10
clip_grad_norm = None
weight_decay = 0
draw_loss_pic = False
loss_decimal_place = 4

Evaluation Hyper Parameters:
eval_setting = TO_LS,full
group_by_user = True
split_ratio = [0.8, 0.1, 0.1]
leave_one_num = 2
real_time_process = False
metrics = ['NDCG', 'Hit', 'MRR', 'Recall', 'Precision', 'MAP']
topk = [5, 10, 20]
valid_metric = Hit@10
eval_batch_size = 256
metric_decimal_place = 4

Dataset Hyper Parameters:
field_separator = 	
seq_separator =  
USER_ID_FIELD = user_id
ITEM_ID_FIELD = item_id
RATING_FIELD = rating
TIME_FIELD = timestamp
seq_len = None
LABEL_FIELD = label
threshold = None
NEG_PREFIX = neg_
load_col = {'inter': ['user_id', 'item_id', 'rating', 'timestamp']}
unload_col = None
unused_col = None
additional_feat_suffix = None
rm_dup_inter = None
lowest_val = None
highest_val = None
equal_val = None
not_equal_val = None
filter_inter_by_user_or_item = True
max_user_inter_num = None
min_user_inter_num = 5
max_item_inter_num = None
min_item_inter_num = 5
fields_in_same_space = None
preload_weight = None
normalize_field = None
normalize_all = True
ITEM_LIST_LENGTH_FIELD = item_length
LIST_SUFFIX = _list
MAX_ITEM_LIST_LENGTH = 50
POSITION_FIELD = position_id
HEAD_ENTITY_ID_FIELD = head_id
TAIL_ENTITY_ID_FIELD = tail_id
RELATION_ID_FIELD = relation_id
ENTITY_ID_FIELD = entity_id

Other Hyper Parameters: 
valid_metric_bigger = True
n_layers = 2
n_heads = 2
hidden_size = 64
inner_size = 256
hidden_dropout_prob = 0.5
attn_dropout_prob = 0.5
hidden_act = gelu
layer_norm_eps = 1e-12
initializer_range = 0.02
loss_type = CE
lmd = 0.1
SSL_AUG = CLF4SRec
SOURCE_ID_FIELD = source_id
TARGET_ID_FIELD = target_id
benchmark_filename = None
MODEL_TYPE = ModelType.SEQUENTIAL
log_root = ./log/
lmd_sem = 0.1
tau = 1
contrast = us_x
sim = dot
train_r = 1
noise = CLOSE
noise_r = 0
lmd_tf = 0.5
MODEL_INPUT_TYPE = InputType.POINTWISE
eval_type = EvaluatorType.RANKING
device = cuda
train_neg_sample_args = {'strategy': 'none'}
log_dir = /home_sda1/ghh/RS/CLF4SRec-main-0/log/CLF4SRec/ml-100k/bs256-lmd0.1-sem0.1-us_x-Jul-20-2025_16-41-53-lr0.001-l20-tau1-dot-DPh0.5-DPa0.5


Sun 20 Jul 2025 16:41:53 INFO  ml-100k
The number of users: 944
Average actions of users: 105.28844114528101
The number of items: 1350
Average actions of items: 73.6004447739066
The number of inters: 99287
The sparsity of the dataset: 92.20911801632141%
Remain Fields: ['user_id', 'item_id', 'rating', 'timestamp']
