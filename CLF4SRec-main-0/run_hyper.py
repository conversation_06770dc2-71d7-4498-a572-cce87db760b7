# -*- coding: utf-8 -*-
# @Time   : 2020/7/24 15:57
# <AUTHOR> <PERSON><PERSON><PERSON>
# @Email  : <EMAIL>
# @File   : run_hyper.py
# UPDATE:
# @Time   : 2020/8/20 21:17, 2020/8/29
# <AUTHOR> <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>
# @Email  : <EMAIL>, <EMAIL>

import argparse

from recbole.trainer import HyperTuning
from recbole.quick_start import objective_function


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--config_files', type=str, default='seq.yaml', help='fixed config files')
    parser.add_argument('--params_file', type=str, default='hu.test', help='parameters file')
    args, _ = parser.parse_known_args()

    # plz set algo='exhaustive' to use exhaustive search, in this case, max_evals is auto set
    config_file_list = args.config_files.strip().split(' ') if args.config_files else None
    hp = HyperTuning(objective_function, algo='exhaustive',
                     params_file=args.params_file, fixed_config_file_list=config_file_list)
    hp.run()
    hp.export_result(output_file='hyper_example.result')
    print('best params: ', hp.best_params)
    print('best result: ')
    print(hp.params2result[hp.params2str(hp.best_params)])


if __name__ == '__main__':
    main()
