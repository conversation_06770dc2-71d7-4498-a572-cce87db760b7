Namespace(data_dir='./data/', output_dir='output/', data_name='Toys_and_Games', do_eval=False, load_model=None, model_name='FMLPRec', hidden_size=64, num_hidden_layers=2, num_attention_heads=2, hidden_act='gelu', attention_probs_dropout_prob=0.5, hidden_dropout_prob=0.5, initializer_range=0.02, max_seq_length=50, no_filters=False, lr=0.001, batch_size=256, epochs=200, no_cuda=False, log_freq=1, full_sort=False, patience=10, seed=42, weight_decay=0.0, adam_beta1=0.9, adam_beta2=0.999, gpu_id='0', variance=5, cuda_condition=True, data_file='./data/Toys_and_Games.txt', sample_file='./data/Toys_and_Games_sample.txt', item_size=11925, log_file='output/FMLPRec-Toys_and_Games-Jul-13-2025_20-38-27.txt')
数据集信息: 用户数量=19412, 物品数量=11925
{'epoch': 0, 'rec_loss': '1.2938', 'train_time': '4.27秒'}
{'Epoch': 0, 'HIT@1': '0.0592', 'NDCG@1': '0.0592', 'HIT@5': '0.1832', 'NDCG@5': '0.1217', 'HIT@10': '0.2877', 'NDCG@10': '0.1552', 'MRR': '0.1372'}
test_time: 0.69秒
Epoch 0 运行时间: 5.05秒
{'epoch': 1, 'rec_loss': '1.1696', 'train_time': '3.85秒'}
{'Epoch': 1, 'HIT@1': '0.0850', 'NDCG@1': '0.0850', 'HIT@5': '0.2462', 'NDCG@5': '0.1671', 'HIT@10': '0.3607', 'NDCG@10': '0.2040', 'MRR': '0.1769'}
test_time: 0.46秒
Epoch 1 运行时间: 4.40秒
{'epoch': 2, 'rec_loss': '1.0698', 'train_time': '4.13秒'}
{'Epoch': 2, 'HIT@1': '0.1119', 'NDCG@1': '0.1119', 'HIT@5': '0.2926', 'NDCG@5': '0.2045', 'HIT@10': '0.4068', 'NDCG@10': '0.2413', 'MRR': '0.2102'}
test_time: 0.42秒
Epoch 2 运行时间: 4.64秒
{'epoch': 3, 'rec_loss': '0.9907', 'train_time': '4.28秒'}
{'Epoch': 3, 'HIT@1': '0.1322', 'NDCG@1': '0.1322', 'HIT@5': '0.3229', 'NDCG@5': '0.2299', 'HIT@10': '0.4380', 'NDCG@10': '0.2671', 'MRR': '0.2333'}
test_time: 0.47秒
Epoch 3 运行时间: 4.84秒
{'epoch': 4, 'rec_loss': '0.9204', 'train_time': '4.03秒'}
{'Epoch': 4, 'HIT@1': '0.1418', 'NDCG@1': '0.1418', 'HIT@5': '0.3390', 'NDCG@5': '0.2430', 'HIT@10': '0.4537', 'NDCG@10': '0.2801', 'MRR': '0.2451'}
test_time: 0.43秒
Epoch 4 运行时间: 4.55秒
{'epoch': 5, 'rec_loss': '0.8617', 'train_time': '4.39秒'}
{'Epoch': 5, 'HIT@1': '0.1484', 'NDCG@1': '0.1484', 'HIT@5': '0.3565', 'NDCG@5': '0.2555', 'HIT@10': '0.4699', 'NDCG@10': '0.2921', 'MRR': '0.2552'}
test_time: 0.50秒
Epoch 5 运行时间: 4.99秒
{'epoch': 6, 'rec_loss': '0.8104', 'train_time': '4.49秒'}
{'Epoch': 6, 'HIT@1': '0.1617', 'NDCG@1': '0.1617', 'HIT@5': '0.3722', 'NDCG@5': '0.2705', 'HIT@10': '0.4801', 'NDCG@10': '0.3053', 'MRR': '0.2690'}
test_time: 0.51秒
Epoch 6 运行时间: 5.10秒
{'epoch': 7, 'rec_loss': '0.7657', 'train_time': '4.52秒'}
{'Epoch': 7, 'HIT@1': '0.1659', 'NDCG@1': '0.1659', 'HIT@5': '0.3746', 'NDCG@5': '0.2734', 'HIT@10': '0.4840', 'NDCG@10': '0.3089', 'MRR': '0.2725'}
test_time: 0.47秒
Epoch 7 运行时间: 5.08秒
{'epoch': 8, 'rec_loss': '0.7342', 'train_time': '4.25秒'}
{'Epoch': 8, 'HIT@1': '0.1708', 'NDCG@1': '0.1708', 'HIT@5': '0.3779', 'NDCG@5': '0.2782', 'HIT@10': '0.4841', 'NDCG@10': '0.3126', 'MRR': '0.2775'}
test_time: 0.57秒
Epoch 8 运行时间: 4.91秒
{'epoch': 9, 'rec_loss': '0.7012', 'train_time': '4.42秒'}
{'Epoch': 9, 'HIT@1': '0.1726', 'NDCG@1': '0.1726', 'HIT@5': '0.3873', 'NDCG@5': '0.2842', 'HIT@10': '0.4921', 'NDCG@10': '0.3181', 'MRR': '0.2817'}
test_time: 0.52秒
Epoch 9 运行时间: 5.03秒
{'epoch': 10, 'rec_loss': '0.6770', 'train_time': '4.37秒'}
{'Epoch': 10, 'HIT@1': '0.1828', 'NDCG@1': '0.1828', 'HIT@5': '0.3936', 'NDCG@5': '0.2926', 'HIT@10': '0.4989', 'NDCG@10': '0.3266', 'MRR': '0.2907'}
test_time: 0.55秒
Epoch 10 运行时间: 5.03秒
{'epoch': 11, 'rec_loss': '0.6544', 'train_time': '4.15秒'}
{'Epoch': 11, 'HIT@1': '0.1827', 'NDCG@1': '0.1827', 'HIT@5': '0.3930', 'NDCG@5': '0.2920', 'HIT@10': '0.4986', 'NDCG@10': '0.3262', 'MRR': '0.2902'}
test_time: 0.72秒
Epoch 11 运行时间: 4.96秒
{'epoch': 12, 'rec_loss': '0.6433', 'train_time': '4.18秒'}
{'Epoch': 12, 'HIT@1': '0.1845', 'NDCG@1': '0.1845', 'HIT@5': '0.3938', 'NDCG@5': '0.2935', 'HIT@10': '0.5030', 'NDCG@10': '0.3288', 'MRR': '0.2923'}
test_time: 0.54秒
Epoch 12 运行时间: 4.81秒
{'epoch': 13, 'rec_loss': '0.6272', 'train_time': '4.39秒'}
{'Epoch': 13, 'HIT@1': '0.1867', 'NDCG@1': '0.1867', 'HIT@5': '0.3993', 'NDCG@5': '0.2971', 'HIT@10': '0.5048', 'NDCG@10': '0.3311', 'MRR': '0.2947'}
test_time: 0.50秒
Epoch 13 运行时间: 4.99秒
{'epoch': 14, 'rec_loss': '0.6131', 'train_time': '4.40秒'}
{'Epoch': 14, 'HIT@1': '0.1907', 'NDCG@1': '0.1907', 'HIT@5': '0.4030', 'NDCG@5': '0.3010', 'HIT@10': '0.5052', 'NDCG@10': '0.3340', 'MRR': '0.2983'}
test_time: 0.51秒
Epoch 14 运行时间: 5.02秒
{'epoch': 15, 'rec_loss': '0.6028', 'train_time': '4.23秒'}
{'Epoch': 15, 'HIT@1': '0.1927', 'NDCG@1': '0.1927', 'HIT@5': '0.4004', 'NDCG@5': '0.3008', 'HIT@10': '0.5078', 'NDCG@10': '0.3355', 'MRR': '0.2994'}
test_time: 0.57秒
Epoch 15 运行时间: 4.89秒
{'epoch': 16, 'rec_loss': '0.5911', 'train_time': '4.34秒'}
{'Epoch': 16, 'HIT@1': '0.1928', 'NDCG@1': '0.1928', 'HIT@5': '0.4011', 'NDCG@5': '0.3016', 'HIT@10': '0.5044', 'NDCG@10': '0.3350', 'MRR': '0.2998'}
test_time: 0.56秒
Epoch 16 运行时间: 5.00秒
{'epoch': 17, 'rec_loss': '0.5809', 'train_time': '4.36秒'}
{'Epoch': 17, 'HIT@1': '0.1918', 'NDCG@1': '0.1918', 'HIT@5': '0.4047', 'NDCG@5': '0.3031', 'HIT@10': '0.5083', 'NDCG@10': '0.3365', 'MRR': '0.3005'}
test_time: 0.52秒
Epoch 17 运行时间: 4.99秒
{'epoch': 18, 'rec_loss': '0.5734', 'train_time': '4.49秒'}
{'Epoch': 18, 'HIT@1': '0.1959', 'NDCG@1': '0.1959', 'HIT@5': '0.4084', 'NDCG@5': '0.3062', 'HIT@10': '0.5117', 'NDCG@10': '0.3396', 'MRR': '0.3035'}
test_time: 0.52秒
Epoch 18 运行时间: 5.11秒
{'epoch': 19, 'rec_loss': '0.5681', 'train_time': '4.37秒'}
{'Epoch': 19, 'HIT@1': '0.1944', 'NDCG@1': '0.1944', 'HIT@5': '0.4069', 'NDCG@5': '0.3054', 'HIT@10': '0.5129', 'NDCG@10': '0.3396', 'MRR': '0.3029'}
test_time: 0.51秒
Epoch 19 运行时间: 4.97秒
{'epoch': 20, 'rec_loss': '0.5571', 'train_time': '4.43秒'}
{'Epoch': 20, 'HIT@1': '0.1957', 'NDCG@1': '0.1957', 'HIT@5': '0.4091', 'NDCG@5': '0.3073', 'HIT@10': '0.5133', 'NDCG@10': '0.3408', 'MRR': '0.3046'}
test_time: 0.52秒
Epoch 20 运行时间: 5.05秒
{'epoch': 21, 'rec_loss': '0.5533', 'train_time': '4.35秒'}
{'Epoch': 21, 'HIT@1': '0.1972', 'NDCG@1': '0.1972', 'HIT@5': '0.4096', 'NDCG@5': '0.3077', 'HIT@10': '0.5124', 'NDCG@10': '0.3408', 'MRR': '0.3048'}
test_time: 0.45秒
Epoch 21 运行时间: 4.89秒
{'epoch': 22, 'rec_loss': '0.5484', 'train_time': '3.93秒'}
{'Epoch': 22, 'HIT@1': '0.2021', 'NDCG@1': '0.2021', 'HIT@5': '0.4124', 'NDCG@5': '0.3119', 'HIT@10': '0.5176', 'NDCG@10': '0.3458', 'MRR': '0.3095'}
test_time: 0.55秒
Epoch 22 运行时间: 4.57秒
{'epoch': 23, 'rec_loss': '0.5374', 'train_time': '4.12秒'}
{'Epoch': 23, 'HIT@1': '0.2005', 'NDCG@1': '0.2005', 'HIT@5': '0.4140', 'NDCG@5': '0.3123', 'HIT@10': '0.5166', 'NDCG@10': '0.3455', 'MRR': '0.3093'}
test_time: 0.49秒
Epoch 23 运行时间: 4.71秒
{'epoch': 24, 'rec_loss': '0.5377', 'train_time': '4.06秒'}
{'Epoch': 24, 'HIT@1': '0.2012', 'NDCG@1': '0.2012', 'HIT@5': '0.4165', 'NDCG@5': '0.3143', 'HIT@10': '0.5194', 'NDCG@10': '0.3476', 'MRR': '0.3110'}
test_time: 0.63秒
Epoch 24 运行时间: 4.79秒
{'epoch': 25, 'rec_loss': '0.5314', 'train_time': '4.25秒'}
{'Epoch': 25, 'HIT@1': '0.2032', 'NDCG@1': '0.2032', 'HIT@5': '0.4119', 'NDCG@5': '0.3126', 'HIT@10': '0.5167', 'NDCG@10': '0.3465', 'MRR': '0.3108'}
test_time: 0.63秒
Epoch 25 运行时间: 4.97秒
{'epoch': 26, 'rec_loss': '0.5305', 'train_time': '4.27秒'}
{'Epoch': 26, 'HIT@1': '0.2041', 'NDCG@1': '0.2041', 'HIT@5': '0.4153', 'NDCG@5': '0.3147', 'HIT@10': '0.5193', 'NDCG@10': '0.3484', 'MRR': '0.3122'}
test_time: 0.44秒
Epoch 26 运行时间: 4.80秒
{'epoch': 27, 'rec_loss': '0.5240', 'train_time': '4.03秒'}
{'Epoch': 27, 'HIT@1': '0.2036', 'NDCG@1': '0.2036', 'HIT@5': '0.4143', 'NDCG@5': '0.3139', 'HIT@10': '0.5209', 'NDCG@10': '0.3484', 'MRR': '0.3117'}
test_time: 0.47秒
Epoch 27 运行时间: 4.59秒
{'epoch': 28, 'rec_loss': '0.5180', 'train_time': '3.92秒'}
{'Epoch': 28, 'HIT@1': '0.2053', 'NDCG@1': '0.2053', 'HIT@5': '0.4156', 'NDCG@5': '0.3150', 'HIT@10': '0.5196', 'NDCG@10': '0.3486', 'MRR': '0.3125'}
test_time: 0.46秒
Epoch 28 运行时间: 4.47秒
{'epoch': 29, 'rec_loss': '0.5148', 'train_time': '4.26秒'}
{'Epoch': 29, 'HIT@1': '0.2057', 'NDCG@1': '0.2057', 'HIT@5': '0.4138', 'NDCG@5': '0.3147', 'HIT@10': '0.5184', 'NDCG@10': '0.3485', 'MRR': '0.3128'}
test_time: 0.47秒
Epoch 29 运行时间: 4.82秒
{'epoch': 30, 'rec_loss': '0.5111', 'train_time': '3.97秒'}
{'Epoch': 30, 'HIT@1': '0.2045', 'NDCG@1': '0.2045', 'HIT@5': '0.4191', 'NDCG@5': '0.3171', 'HIT@10': '0.5207', 'NDCG@10': '0.3499', 'MRR': '0.3138'}
test_time: 0.46秒
Epoch 30 运行时间: 4.53秒
{'epoch': 31, 'rec_loss': '0.5092', 'train_time': '4.92秒'}
{'Epoch': 31, 'HIT@1': '0.2044', 'NDCG@1': '0.2044', 'HIT@5': '0.4166', 'NDCG@5': '0.3154', 'HIT@10': '0.5215', 'NDCG@10': '0.3493', 'MRR': '0.3127'}
test_time: 0.93秒
Epoch 31 运行时间: 5.96秒
{'epoch': 32, 'rec_loss': '0.5053', 'train_time': '4.31秒'}
{'Epoch': 32, 'HIT@1': '0.2104', 'NDCG@1': '0.2104', 'HIT@5': '0.4214', 'NDCG@5': '0.3205', 'HIT@10': '0.5232', 'NDCG@10': '0.3534', 'MRR': '0.3175'}
test_time: 0.60秒
Epoch 32 运行时间: 5.01秒
{'epoch': 33, 'rec_loss': '0.5057', 'train_time': '4.13秒'}
{'Epoch': 33, 'HIT@1': '0.2078', 'NDCG@1': '0.2078', 'HIT@5': '0.4221', 'NDCG@5': '0.3196', 'HIT@10': '0.5250', 'NDCG@10': '0.3528', 'MRR': '0.3160'}
test_time: 0.52秒
Epoch 33 运行时间: 4.76秒
{'epoch': 34, 'rec_loss': '0.4971', 'train_time': '4.08秒'}
{'Epoch': 34, 'HIT@1': '0.2096', 'NDCG@1': '0.2096', 'HIT@5': '0.4210', 'NDCG@5': '0.3200', 'HIT@10': '0.5258', 'NDCG@10': '0.3539', 'MRR': '0.3172'}
test_time: 0.54秒
Epoch 34 运行时间: 4.71秒
{'epoch': 35, 'rec_loss': '0.4924', 'train_time': '4.01秒'}
{'Epoch': 35, 'HIT@1': '0.2098', 'NDCG@1': '0.2098', 'HIT@5': '0.4232', 'NDCG@5': '0.3219', 'HIT@10': '0.5254', 'NDCG@10': '0.3549', 'MRR': '0.3185'}
test_time: 0.45秒
Epoch 35 运行时间: 4.55秒
{'epoch': 36, 'rec_loss': '0.4893', 'train_time': '4.12秒'}
{'Epoch': 36, 'HIT@1': '0.2117', 'NDCG@1': '0.2117', 'HIT@5': '0.4242', 'NDCG@5': '0.3233', 'HIT@10': '0.5232', 'NDCG@10': '0.3553', 'MRR': '0.3199'}
test_time: 0.59秒
Epoch 36 运行时间: 4.80秒
{'epoch': 37, 'rec_loss': '0.4893', 'train_time': '4.08秒'}
{'Epoch': 37, 'HIT@1': '0.2104', 'NDCG@1': '0.2104', 'HIT@5': '0.4228', 'NDCG@5': '0.3215', 'HIT@10': '0.5251', 'NDCG@10': '0.3545', 'MRR': '0.3183'}
test_time: 0.45秒
Epoch 37 运行时间: 4.62秒
{'epoch': 38, 'rec_loss': '0.4837', 'train_time': '4.26秒'}
{'Epoch': 38, 'HIT@1': '0.2101', 'NDCG@1': '0.2101', 'HIT@5': '0.4204', 'NDCG@5': '0.3202', 'HIT@10': '0.5248', 'NDCG@10': '0.3539', 'MRR': '0.3176'}
test_time: 0.51秒
Epoch 38 运行时间: 4.86秒
{'epoch': 39, 'rec_loss': '0.4853', 'train_time': '4.18秒'}
{'Epoch': 39, 'HIT@1': '0.2118', 'NDCG@1': '0.2118', 'HIT@5': '0.4207', 'NDCG@5': '0.3212', 'HIT@10': '0.5256', 'NDCG@10': '0.3552', 'MRR': '0.3190'}
test_time: 0.57秒
Epoch 39 运行时间: 4.84秒
{'epoch': 40, 'rec_loss': '0.4843', 'train_time': '4.02秒'}
{'Epoch': 40, 'HIT@1': '0.2103', 'NDCG@1': '0.2103', 'HIT@5': '0.4223', 'NDCG@5': '0.3213', 'HIT@10': '0.5235', 'NDCG@10': '0.3540', 'MRR': '0.3181'}
test_time: 0.43秒
Epoch 40 运行时间: 4.54秒
{'epoch': 41, 'rec_loss': '0.4800', 'train_time': '4.23秒'}
{'Epoch': 41, 'HIT@1': '0.2115', 'NDCG@1': '0.2115', 'HIT@5': '0.4235', 'NDCG@5': '0.3224', 'HIT@10': '0.5240', 'NDCG@10': '0.3547', 'MRR': '0.3190'}
test_time: 0.54秒
Epoch 41 运行时间: 4.86秒
{'epoch': 42, 'rec_loss': '0.4747', 'train_time': '3.94秒'}
{'Epoch': 42, 'HIT@1': '0.2134', 'NDCG@1': '0.2134', 'HIT@5': '0.4221', 'NDCG@5': '0.3231', 'HIT@10': '0.5265', 'NDCG@10': '0.3568', 'MRR': '0.3207'}
test_time: 0.61秒
Epoch 42 运行时间: 4.65秒
{'epoch': 43, 'rec_loss': '0.4776', 'train_time': '4.61秒'}
{'Epoch': 43, 'HIT@1': '0.2151', 'NDCG@1': '0.2151', 'HIT@5': '0.4264', 'NDCG@5': '0.3262', 'HIT@10': '0.5283', 'NDCG@10': '0.3591', 'MRR': '0.3230'}
test_time: 0.63秒
Epoch 43 运行时间: 5.34秒
{'epoch': 44, 'rec_loss': '0.4776', 'train_time': '4.37秒'}
{'Epoch': 44, 'HIT@1': '0.2167', 'NDCG@1': '0.2167', 'HIT@5': '0.4254', 'NDCG@5': '0.3264', 'HIT@10': '0.5290', 'NDCG@10': '0.3599', 'MRR': '0.3238'}
test_time: 0.45秒
Epoch 44 运行时间: 4.91秒
{'epoch': 45, 'rec_loss': '0.4764', 'train_time': '4.03秒'}
{'Epoch': 45, 'HIT@1': '0.2126', 'NDCG@1': '0.2126', 'HIT@5': '0.4209', 'NDCG@5': '0.3223', 'HIT@10': '0.5252', 'NDCG@10': '0.3560', 'MRR': '0.3201'}
test_time: 0.60秒
Epoch 45 运行时间: 4.74秒
{'epoch': 46, 'rec_loss': '0.4702', 'train_time': '4.19秒'}
{'Epoch': 46, 'HIT@1': '0.2148', 'NDCG@1': '0.2148', 'HIT@5': '0.4246', 'NDCG@5': '0.3252', 'HIT@10': '0.5273', 'NDCG@10': '0.3583', 'MRR': '0.3224'}
test_time: 0.58秒
Epoch 46 运行时间: 4.87秒
{'epoch': 47, 'rec_loss': '0.4724', 'train_time': '4.21秒'}
{'Epoch': 47, 'HIT@1': '0.2167', 'NDCG@1': '0.2167', 'HIT@5': '0.4271', 'NDCG@5': '0.3270', 'HIT@10': '0.5279', 'NDCG@10': '0.3595', 'MRR': '0.3237'}
test_time: 0.55秒
Epoch 47 运行时间: 4.85秒
{'epoch': 48, 'rec_loss': '0.4667', 'train_time': '4.16秒'}
{'Epoch': 48, 'HIT@1': '0.2167', 'NDCG@1': '0.2167', 'HIT@5': '0.4251', 'NDCG@5': '0.3259', 'HIT@10': '0.5280', 'NDCG@10': '0.3592', 'MRR': '0.3232'}
test_time: 0.56秒
Epoch 48 运行时间: 4.82秒
{'epoch': 49, 'rec_loss': '0.4662', 'train_time': '4.40秒'}
{'Epoch': 49, 'HIT@1': '0.2169', 'NDCG@1': '0.2169', 'HIT@5': '0.4263', 'NDCG@5': '0.3266', 'HIT@10': '0.5297', 'NDCG@10': '0.3600', 'MRR': '0.3237'}
test_time: 0.58秒
Epoch 49 运行时间: 5.10秒
{'epoch': 50, 'rec_loss': '0.4644', 'train_time': '4.46秒'}
{'Epoch': 50, 'HIT@1': '0.2148', 'NDCG@1': '0.2148', 'HIT@5': '0.4270', 'NDCG@5': '0.3258', 'HIT@10': '0.5268', 'NDCG@10': '0.3580', 'MRR': '0.3222'}
test_time: 0.58秒
Epoch 50 运行时间: 5.14秒
{'epoch': 51, 'rec_loss': '0.4616', 'train_time': '4.07秒'}
{'Epoch': 51, 'HIT@1': '0.2148', 'NDCG@1': '0.2148', 'HIT@5': '0.4278', 'NDCG@5': '0.3265', 'HIT@10': '0.5296', 'NDCG@10': '0.3594', 'MRR': '0.3229'}
test_time: 0.51秒
Epoch 51 运行时间: 4.67秒
{'epoch': 52, 'rec_loss': '0.4600', 'train_time': '4.33秒'}
{'Epoch': 52, 'HIT@1': '0.2177', 'NDCG@1': '0.2177', 'HIT@5': '0.4269', 'NDCG@5': '0.3273', 'HIT@10': '0.5306', 'NDCG@10': '0.3609', 'MRR': '0.3245'}
test_time: 0.51秒
Epoch 52 运行时间: 4.94秒
{'epoch': 53, 'rec_loss': '0.4587', 'train_time': '4.36秒'}
{'Epoch': 53, 'HIT@1': '0.2186', 'NDCG@1': '0.2186', 'HIT@5': '0.4264', 'NDCG@5': '0.3271', 'HIT@10': '0.5295', 'NDCG@10': '0.3604', 'MRR': '0.3243'}
test_time: 0.58秒
Epoch 53 运行时间: 5.05秒
{'epoch': 54, 'rec_loss': '0.4594', 'train_time': '4.39秒'}
{'Epoch': 54, 'HIT@1': '0.2186', 'NDCG@1': '0.2186', 'HIT@5': '0.4266', 'NDCG@5': '0.3275', 'HIT@10': '0.5266', 'NDCG@10': '0.3598', 'MRR': '0.3247'}
test_time: 0.52秒
Epoch 54 运行时间: 5.01秒
{'epoch': 55, 'rec_loss': '0.4551', 'train_time': '4.12秒'}
{'Epoch': 55, 'HIT@1': '0.2164', 'NDCG@1': '0.2164', 'HIT@5': '0.4285', 'NDCG@5': '0.3280', 'HIT@10': '0.5286', 'NDCG@10': '0.3603', 'MRR': '0.3244'}
test_time: 0.58秒
Epoch 55 运行时间: 4.79秒
{'epoch': 56, 'rec_loss': '0.4546', 'train_time': '4.03秒'}
{'Epoch': 56, 'HIT@1': '0.2207', 'NDCG@1': '0.2207', 'HIT@5': '0.4289', 'NDCG@5': '0.3297', 'HIT@10': '0.5281', 'NDCG@10': '0.3617', 'MRR': '0.3266'}
test_time: 0.60秒
Epoch 56 运行时间: 4.74秒
{'epoch': 57, 'rec_loss': '0.4509', 'train_time': '4.39秒'}
{'Epoch': 57, 'HIT@1': '0.2199', 'NDCG@1': '0.2199', 'HIT@5': '0.4291', 'NDCG@5': '0.3293', 'HIT@10': '0.5293', 'NDCG@10': '0.3616', 'MRR': '0.3259'}
test_time: 0.54秒
Epoch 57 运行时间: 5.03秒
{'epoch': 58, 'rec_loss': '0.4527', 'train_time': '4.57秒'}
{'Epoch': 58, 'HIT@1': '0.2206', 'NDCG@1': '0.2206', 'HIT@5': '0.4296', 'NDCG@5': '0.3306', 'HIT@10': '0.5304', 'NDCG@10': '0.3632', 'MRR': '0.3276'}
test_time: 0.44秒
Epoch 58 运行时间: 5.10秒
{'epoch': 59, 'rec_loss': '0.4520', 'train_time': '4.02秒'}
{'Epoch': 59, 'HIT@1': '0.2185', 'NDCG@1': '0.2185', 'HIT@5': '0.4302', 'NDCG@5': '0.3298', 'HIT@10': '0.5294', 'NDCG@10': '0.3617', 'MRR': '0.3260'}
test_time: 0.44秒
Epoch 59 运行时间: 4.55秒
{'epoch': 60, 'rec_loss': '0.4472', 'train_time': '4.28秒'}
{'Epoch': 60, 'HIT@1': '0.2196', 'NDCG@1': '0.2196', 'HIT@5': '0.4293', 'NDCG@5': '0.3293', 'HIT@10': '0.5305', 'NDCG@10': '0.3622', 'MRR': '0.3261'}
test_time: 0.41秒
Epoch 60 运行时间: 4.78秒
{'epoch': 61, 'rec_loss': '0.4449', 'train_time': '4.38秒'}
{'Epoch': 61, 'HIT@1': '0.2162', 'NDCG@1': '0.2162', 'HIT@5': '0.4303', 'NDCG@5': '0.3289', 'HIT@10': '0.5283', 'NDCG@10': '0.3606', 'MRR': '0.3249'}
test_time: 0.61秒
Epoch 61 运行时间: 5.09秒
{'epoch': 62, 'rec_loss': '0.4443', 'train_time': '4.47秒'}
{'Epoch': 62, 'HIT@1': '0.2200', 'NDCG@1': '0.2200', 'HIT@5': '0.4290', 'NDCG@5': '0.3301', 'HIT@10': '0.5277', 'NDCG@10': '0.3620', 'MRR': '0.3270'}
test_time: 0.50秒
Epoch 62 运行时间: 5.06秒
{'epoch': 63, 'rec_loss': '0.4430', 'train_time': '4.48秒'}
{'Epoch': 63, 'HIT@1': '0.2210', 'NDCG@1': '0.2210', 'HIT@5': '0.4292', 'NDCG@5': '0.3303', 'HIT@10': '0.5282', 'NDCG@10': '0.3623', 'MRR': '0.3273'}
test_time: 0.52秒
Epoch 63 运行时间: 5.10秒
{'epoch': 64, 'rec_loss': '0.4424', 'train_time': '4.31秒'}
{'Epoch': 64, 'HIT@1': '0.2236', 'NDCG@1': '0.2236', 'HIT@5': '0.4309', 'NDCG@5': '0.3326', 'HIT@10': '0.5298', 'NDCG@10': '0.3645', 'MRR': '0.3296'}
test_time: 0.54秒
Epoch 64 运行时间: 4.95秒
{'epoch': 65, 'rec_loss': '0.4457', 'train_time': '4.17秒'}
{'Epoch': 65, 'HIT@1': '0.2255', 'NDCG@1': '0.2255', 'HIT@5': '0.4307', 'NDCG@5': '0.3330', 'HIT@10': '0.5280', 'NDCG@10': '0.3644', 'MRR': '0.3301'}
test_time: 0.42秒
Epoch 65 运行时间: 4.69秒
{'epoch': 66, 'rec_loss': '0.4385', 'train_time': '3.97秒'}
{'Epoch': 66, 'HIT@1': '0.2240', 'NDCG@1': '0.2240', 'HIT@5': '0.4299', 'NDCG@5': '0.3323', 'HIT@10': '0.5283', 'NDCG@10': '0.3641', 'MRR': '0.3296'}
test_time: 0.48秒
Epoch 66 运行时间: 4.55秒
{'epoch': 67, 'rec_loss': '0.4394', 'train_time': '4.14秒'}
{'Epoch': 67, 'HIT@1': '0.2238', 'NDCG@1': '0.2238', 'HIT@5': '0.4322', 'NDCG@5': '0.3329', 'HIT@10': '0.5327', 'NDCG@10': '0.3653', 'MRR': '0.3297'}
test_time: 0.65秒
Epoch 67 运行时间: 4.89秒
{'epoch': 68, 'rec_loss': '0.4379', 'train_time': '4.05秒'}
{'Epoch': 68, 'HIT@1': '0.2234', 'NDCG@1': '0.2234', 'HIT@5': '0.4319', 'NDCG@5': '0.3328', 'HIT@10': '0.5319', 'NDCG@10': '0.3651', 'MRR': '0.3296'}
test_time: 0.57秒
Epoch 68 运行时间: 4.72秒
{'epoch': 69, 'rec_loss': '0.4370', 'train_time': '4.14秒'}
{'Epoch': 69, 'HIT@1': '0.2236', 'NDCG@1': '0.2236', 'HIT@5': '0.4319', 'NDCG@5': '0.3323', 'HIT@10': '0.5336', 'NDCG@10': '0.3653', 'MRR': '0.3292'}
test_time: 0.43秒
Epoch 69 运行时间: 4.67秒
{'epoch': 70, 'rec_loss': '0.4410', 'train_time': '4.00秒'}
{'Epoch': 70, 'HIT@1': '0.2203', 'NDCG@1': '0.2203', 'HIT@5': '0.4326', 'NDCG@5': '0.3316', 'HIT@10': '0.5308', 'NDCG@10': '0.3633', 'MRR': '0.3276'}
test_time: 0.54秒
Epoch 70 运行时间: 4.63秒
{'epoch': 71, 'rec_loss': '0.4352', 'train_time': '4.34秒'}
{'Epoch': 71, 'HIT@1': '0.2238', 'NDCG@1': '0.2238', 'HIT@5': '0.4314', 'NDCG@5': '0.3327', 'HIT@10': '0.5298', 'NDCG@10': '0.3645', 'MRR': '0.3296'}
test_time: 0.40秒
Epoch 71 运行时间: 4.84秒
{'epoch': 72, 'rec_loss': '0.4357', 'train_time': '4.26秒'}
{'Epoch': 72, 'HIT@1': '0.2205', 'NDCG@1': '0.2205', 'HIT@5': '0.4304', 'NDCG@5': '0.3306', 'HIT@10': '0.5291', 'NDCG@10': '0.3625', 'MRR': '0.3272'}
test_time: 0.43秒
Epoch 72 运行时间: 4.79秒
{'epoch': 73, 'rec_loss': '0.4318', 'train_time': '3.93秒'}
{'Epoch': 73, 'HIT@1': '0.2221', 'NDCG@1': '0.2221', 'HIT@5': '0.4297', 'NDCG@5': '0.3311', 'HIT@10': '0.5301', 'NDCG@10': '0.3636', 'MRR': '0.3283'}
test_time: 0.59秒
Epoch 73 运行时间: 4.62秒
{'epoch': 74, 'rec_loss': '0.4324', 'train_time': '4.26秒'}
{'Epoch': 74, 'HIT@1': '0.2238', 'NDCG@1': '0.2238', 'HIT@5': '0.4308', 'NDCG@5': '0.3322', 'HIT@10': '0.5342', 'NDCG@10': '0.3656', 'MRR': '0.3294'}
test_time: 0.51秒
Epoch 74 运行时间: 4.87秒
{'epoch': 75, 'rec_loss': '0.4343', 'train_time': '4.09秒'}
{'Epoch': 75, 'HIT@1': '0.2223', 'NDCG@1': '0.2223', 'HIT@5': '0.4305', 'NDCG@5': '0.3316', 'HIT@10': '0.5337', 'NDCG@10': '0.3649', 'MRR': '0.3287'}
test_time: 0.45秒
Epoch 75 运行时间: 4.64秒
{'Epoch': 0, 'HIT@1': '0.1981', 'NDCG@1': '0.1981', 'HIT@5': '0.3898', 'NDCG@5': '0.2984', 'HIT@10': '0.4873', 'NDCG@10': '0.3299', 'MRR': '0.2984'}
test_time: 0.46秒
FMLPRec-Toys_and_Games-Jul-13-2025_20-38-27
{'Epoch': 0, 'HIT@1': '0.1981', 'NDCG@1': '0.1981', 'HIT@5': '0.3898', 'NDCG@5': '0.2984', 'HIT@10': '0.4873', 'NDCG@10': '0.3299', 'MRR': '0.2984'}
